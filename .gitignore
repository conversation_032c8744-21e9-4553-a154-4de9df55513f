# The directory Mix will write compiled artifacts to.
/_build/

priv/*sqlite*

test/sample_app/_build
test/sample_app/deps
test/sample_app/priv/db*

# If you run "mix test --cover", coverage assets end up here.
/cover/

# The directory Mix downloads your dependencies sources to.
/deps/

# Where third-party dependencies like ExDoc output generated docs.
/doc/

# Ignore .fetch files in case you like to edit your project deps locally.
/.fetch

# If the VM crashes, it generates a dump, let's ignore it too.
erl_crash.dump

# Also ignore archive artifacts (built via "mix archive.build").
*.ez

# Ignore package tarball (built via "mix hex.build").
drops-*.tar

# Temporary files, for example, from tests.
/tmp/

mix.lock
