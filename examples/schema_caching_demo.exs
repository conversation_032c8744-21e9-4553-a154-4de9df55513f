# Schema Caching Demo
#
# This script demonstrates the persistent schema caching functionality in Drops.Relation.
# The cache uses JSON files stored in tmp/cache/drops_schema/
# to persist across application restarts.
# Run with: mix run examples/schema_caching_demo.exs

# Start the application to ensure cache is running
Application.ensure_all_started(:drops)

alias Drops.Relation.SchemaCache

IO.puts("=== Drops.Relation Schema Caching Demo ===\n")

# Check if cache is enabled
IO.puts("1. Cache Status:")
IO.puts("   Enabled: #{SchemaCache.enabled?()}")
IO.puts("   Config: #{inspect(SchemaCache.config())}")

# Show initial cache stats
IO.puts("\n2. Initial Cache Stats:")
IO.puts("   Cache is file-based, no runtime stats available")

# Simulate some cache operations
IO.puts("\n3. Simulating Schema Inference (Cache Misses):")

# Mock inference function that simulates expensive database introspection
mock_inference = fn table_name ->
  IO.puts("   🔍 Performing expensive inference for '#{table_name}'...")
  # Simulate database query time
  Process.sleep(10)
  {:mock_ecto_schema, :mock_drops_schema}
end

# First calls - should be cache misses
IO.puts("   🔍 Checking cache for 'users'...")
result1 = SchemaCache.get_cached_schema(MyApp.Repo, "users")

if result1,
  do: IO.puts("   ✓ Cache hit for 'users'"),
  else: IO.puts("   ✗ Cache miss for 'users'")

IO.puts("   🔍 Checking cache for 'posts'...")
result2 = SchemaCache.get_cached_schema(MyApp.Repo, "posts")

if result2,
  do: IO.puts("   ✓ Cache hit for 'posts'"),
  else: IO.puts("   ✗ Cache miss for 'posts'")

IO.puts("   🔍 Checking cache for 'comments'...")
result3 = SchemaCache.get_cached_schema(MyApp.Repo, "comments")

if result3,
  do: IO.puts("   ✓ Cache hit for 'comments'"),
  else: IO.puts("   ✗ Cache miss for 'comments'")

# Cache some schemas
IO.puts("\n4. Caching Some Schemas:")
SchemaCache.cache_schema(MyApp.Repo, "users", mock_inference.("users"))
SchemaCache.cache_schema(MyApp.Repo, "posts", mock_inference.("posts"))
SchemaCache.cache_schema(MyApp.Repo, "comments", mock_inference.("comments"))

IO.puts("\n5. Checking Cache Again (Should be hits):")

# Second calls - should be cache hits
start_time = System.monotonic_time(:millisecond)

result1 = SchemaCache.get_cached_schema(MyApp.Repo, "users")

if result1,
  do: IO.puts("   ✓ Cache hit for 'users'"),
  else: IO.puts("   ✗ Cache miss for 'users'")

result2 = SchemaCache.get_cached_schema(MyApp.Repo, "posts")

if result2,
  do: IO.puts("   ✓ Cache hit for 'posts'"),
  else: IO.puts("   ✗ Cache miss for 'posts'")

result3 = SchemaCache.get_cached_schema(MyApp.Repo, "comments")

if result3,
  do: IO.puts("   ✓ Cache hit for 'comments'"),
  else: IO.puts("   ✗ Cache miss for 'comments'")

end_time = System.monotonic_time(:millisecond)
IO.puts("   ⚡ Cache hits completed in #{end_time - start_time}ms")

# Demonstrate cache management
IO.puts("\n6. Cache Management:")

IO.puts("   Clearing cache for MyApp.Repo...")
SchemaCache.clear_repo_cache(MyApp.Repo)

IO.puts("   Cache cleared successfully")

# Demonstrate configuration
IO.puts("\n7. Configuration Management:")
IO.puts("   Cache enabled: #{SchemaCache.enabled?()}")

IO.puts("\n=== Demo Complete ===")
IO.puts("\nKey Benefits:")
IO.puts("• Schema inference only happens on first compilation or after migrations")
IO.puts("• Cache persists across application restarts using JSON files")
IO.puts("• Subsequent compilations use cached schemas for instant access")
IO.puts("• Automatic cache invalidation when database structure changes")
IO.puts("• Easy cache management and monitoring")
IO.puts("• Solves the real problem: avoiding inference after deployments/restarts")

IO.puts("\nUsage in your application:")

IO.puts("""
# In your relation modules, caching is automatic:
defmodule MyApp.Users do
  use Drops.Relation, repo: MyApp.Repo, name: "users"
  # Schema inference happens once, then cached
end

# Manual cache management:
Drops.Relation.SchemaCache.clear_repo_cache(MyApp.Repo)        # Clear specific repo
Drops.Relation.SchemaCache.clear_all()                         # Clear all caches
Drops.Relation.SchemaCache.warm_up(MyApp.Repo, ["users", "posts"])  # Pre-load schemas

# Configuration:
config :drops,
  schema_cache: [
    enabled: true
  ]
""")
