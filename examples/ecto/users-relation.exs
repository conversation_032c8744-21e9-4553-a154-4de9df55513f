defmodule Users do
  use Drops.Relation, repo: Drops.Repos.Sqlite, name: "users"
end

# Works with maps, changesets and structs for convenience
{:ok, user} = Users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

# PK-based finder, easily accessible
user = Users.get(user.id)

# ✨ Relation composition

users =
  Users
  # Auto-defined index-based finders
  |> Users.get_by_email("<EMAIL>")
  # Generic, composable restriction function
  |> Users.restrict(name: "<PERSON>")

# Implements Enumerable protocol
IO.puts(Enum.count(users))
# => 1

IO.inspect(Enum.map(users, & &1.name))
# => ["<PERSON>"]

# You can materialize to a list on demand easily
loaded_users = Enum.to_list(users)

IO.inspect(loaded_users)
# [
#   %Users.Struct{
#     __meta__: #Ecto.Schema.Metadata<:loaded, "users">,
#     id: 1,
#     name: "<PERSON>",
#     email: "<EMAIL>",
#     inserted_at: ~N[2025-07-07 15:08:11],
#     updated_at: ~N[2025-07-07 15:08:11]
#   }
# ]
