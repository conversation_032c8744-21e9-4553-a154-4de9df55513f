defmodule SaveUser do
  use Drops.Operations.Command, repo: Drops.Repos.Sqlite

  schema(Test.Ecto.TestSchemas.UserSchema)

  steps do
    @impl true
    def execute(%{changeset: changeset}) do
      insert(changeset)
    end
  end

  def validate_changeset(%{changeset: changeset}) do
    changeset
    |> validate_required([:name, :email])
  end
end

valid_params = %{
  name: "<PERSON>",
  email: "<EMAIL>"
}

SaveUser.call(%{params: valid_params})

invalid_params = %{
  email: "<EMAIL>",
  name: ""
}

SaveUser.call(%{params: invalid_params})
