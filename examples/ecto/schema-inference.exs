defmodule Users do
  use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
end

defmodule Associations do
  use Drops.Relation, repo: Drops.TestRepo, name: "associations", infer: true
end

IO.puts("=== Users Relation ===")
IO.puts("Ecto fields: #{inspect(Users.ecto_schema(:fields))}")
IO.puts("Ecto primary key: #{inspect(Users.ecto_schema(:primary_key))}")

users_schema = Users.schema()
IO.puts("Drops schema source: #{users_schema.source}")
IO.puts("Drops primary key: #{inspect(users_schema.primary_key.fields)}")
IO.puts("Drops fields: #{inspect(Enum.map(users_schema.fields, & &1.name))}")
IO.puts("Drops foreign keys: #{inspect(users_schema.foreign_keys)}")
IO.puts("Drops indices: #{inspect(users_schema.indices)}")

IO.puts("\n=== Associations Relation ===")
IO.puts("Ecto fields: #{inspect(Associations.ecto_schema(:fields))}")

associations_schema = Associations.schema()
IO.puts("Drops schema source: #{associations_schema.source}")
IO.puts("Drops primary key: #{inspect(associations_schema.primary_key.fields)}")
IO.puts("Drops fields: #{inspect(Enum.map(associations_schema.fields, & &1.name))}")
IO.puts("Drops foreign keys: #{inspect(associations_schema.foreign_keys)}")
IO.puts("Drops indices: #{inspect(associations_schema.indices)}")

IO.puts("\n=== Performance Test ===")
# Verify that schema() returns the same object reference (stored as module attribute)
schema1 = Users.schema()
schema2 = Users.schema()
IO.puts("Same object reference: #{schema1 === schema2}")

IO.puts("\n=== Field Details ===")

for field <- users_schema.fields do
  IO.puts(
    "Field #{field.name}: type=#{field.type}, ecto_type=#{field.ecto_type}, source=#{field.source}"
  )
end
