# Database Type Mappings for Schema Inference

This document provides comprehensive mappings from database-native column types to Ecto types for SQLite, PostgreSQL, and MySQL, based on analysis of the Sequel ORM codebase.

## SQLite Type Mappings

SQLite has a very limited set of native types. Most types are stored as TEXT, NUMERIC, INTEGER, REAL, or BLOB, with application-level interpretation.

### Core SQLite Types

| SQLite Type | Ecto Type | Notes |
|-------------|-----------|-------|
| `INTEGER` | `:integer` | Native integer storage |
| `REAL` | `:float` | Native floating point |
| `TEXT` | `:string` | Native text storage |
| `BLOB` | `:binary` | Native binary storage |
| `NUMERIC` | `:decimal` | Generic numeric type |

### SQLite Interpreted Types

These types are stored as other native types but interpreted by applications:

| SQLite Type | Storage Type | Ecto Type | Conversion Notes |
|-------------|--------------|-----------|------------------|
| `BO<PERSON>EAN`, `BOOL` | INTEGER | `:boolean` | 0 = false, 1 = true |
| `DATE` | TEXT | `:date` | ISO 8601 date strings |
| `DATETIME`, `TIMESTAMP` | TEXT | `:naive_datetime` | ISO 8601 datetime strings |
| `TIME` | TEXT | `:time` | ISO 8601 time strings |
| `DECIMAL(p,s)` | TEXT | `:decimal` | String representation of decimal |
| `FLOAT`, `DOUBLE` | REAL | `:float` | Floating point numbers |
| `VARCHAR(n)`, `CHAR(n)` | TEXT | `:string` | Text with length constraints |
| `CLOB` | TEXT | `:string` | Character large object |

### SQLite Special Considerations

- **No native boolean type**: Use INTEGER with 0/1 values
- **No native date/time types**: Store as TEXT in ISO 8601 format
- **Type affinity**: SQLite uses type affinity rules, not strict typing
- **Flexible typing**: Columns can store any type regardless of declared type

## PostgreSQL Type Mappings

PostgreSQL has a rich type system with extensive built-in and extensible types.

### Basic PostgreSQL Types

| PostgreSQL Type | Ecto Type | Notes |
|-----------------|-----------|-------|
| `boolean` | `:boolean` | Native boolean |
| `smallint`, `int2` | `:integer` | 16-bit integer |
| `integer`, `int`, `int4` | `:integer` | 32-bit integer |
| `bigint`, `int8` | `:integer` | 64-bit integer |
| `real`, `float4` | `:float` | 32-bit float |
| `double precision`, `float8` | `:float` | 64-bit float |
| `numeric`, `decimal` | `:decimal` | Arbitrary precision |
| `money` | `:decimal` | Currency type |

### PostgreSQL String Types

| PostgreSQL Type | Ecto Type | Notes |
|-----------------|-----------|-------|
| `character varying(n)`, `varchar(n)` | `:string` | Variable length string |
| `character(n)`, `char(n)` | `:string` | Fixed length string |
| `text` | `:string` | Unlimited length string |
| `name` | `:string` | Internal name type |

### PostgreSQL Date/Time Types

| PostgreSQL Type | Ecto Type | Notes |
|-----------------|-----------|-------|
| `date` | `:date` | Date only |
| `time` | `:time` | Time without timezone |
| `time with time zone`, `timetz` | `:time` | Time with timezone |
| `timestamp` | `:naive_datetime` | Timestamp without timezone |
| `timestamp with time zone`, `timestamptz` | `:utc_datetime` | Timestamp with timezone |
| `interval` | Custom type needed | Time intervals |

### PostgreSQL Binary Types

| PostgreSQL Type | Ecto Type | Notes |
|-----------------|-----------|-------|
| `bytea` | `:binary` | Variable length binary |

### PostgreSQL Advanced Types

| PostgreSQL Type | Ecto Type | Notes |
|-----------------|-----------|-------|
| `uuid` | `:binary_id` | UUID type |
| `json` | `:map` | JSON data |
| `jsonb` | `:map` | Binary JSON data |
| `xml` | `:string` | XML documents |
| `inet` | Custom type needed | IP addresses |
| `cidr` | Custom type needed | Network addresses |
| `macaddr` | Custom type needed | MAC addresses |
| `point` | Custom type needed | Geometric point |
| `line` | Custom type needed | Geometric line |
| `lseg` | Custom type needed | Line segment |
| `box` | Custom type needed | Rectangle |
| `path` | Custom type needed | Geometric path |
| `polygon` | Custom type needed | Polygon |
| `circle` | Custom type needed | Circle |

### PostgreSQL Array Types

All PostgreSQL types can be arrays by appending `[]`:

| PostgreSQL Type | Ecto Type | Notes |
|-----------------|-----------|-------|
| `integer[]` | `{:array, :integer}` | Array of integers |
| `text[]` | `{:array, :string}` | Array of strings |
| `boolean[]` | `{:array, :boolean}` | Array of booleans |

### PostgreSQL Range Types

| PostgreSQL Type | Ecto Type | Notes |
|-----------------|-----------|-------|
| `int4range` | Custom type needed | Integer ranges |
| `int8range` | Custom type needed | Bigint ranges |
| `numrange` | Custom type needed | Numeric ranges |
| `tsrange` | Custom type needed | Timestamp ranges |
| `tstzrange` | Custom type needed | Timestamp with timezone ranges |
| `daterange` | Custom type needed | Date ranges |

## MySQL Type Mappings

MySQL has a comprehensive set of numeric, string, date/time, and binary types.

### MySQL Integer Types

| MySQL Type | Ecto Type | Notes |
|------------|-----------|-------|
| `tinyint(1)` | `:boolean` | Special case for boolean |
| `tinyint` | `:integer` | 8-bit integer (-128 to 127) |
| `tinyint unsigned` | `:integer` | 8-bit unsigned (0 to 255) |
| `smallint` | `:integer` | 16-bit integer |
| `smallint unsigned` | `:integer` | 16-bit unsigned |
| `mediumint` | `:integer` | 24-bit integer |
| `mediumint unsigned` | `:integer` | 24-bit unsigned |
| `int`, `integer` | `:integer` | 32-bit integer |
| `int unsigned`, `integer unsigned` | `:integer` | 32-bit unsigned |
| `bigint` | `:integer` | 64-bit integer |
| `bigint unsigned` | `:integer` | 64-bit unsigned |

### MySQL Floating Point Types

| MySQL Type | Ecto Type | Notes |
|------------|-----------|-------|
| `float` | `:float` | Single precision |
| `double`, `double precision` | `:float` | Double precision |
| `decimal(p,s)`, `numeric(p,s)` | `:decimal` | Fixed point |

### MySQL String Types

| MySQL Type | Ecto Type | Notes |
|------------|-----------|-------|
| `char(n)` | `:string` | Fixed length string |
| `varchar(n)` | `:string` | Variable length string |
| `binary(n)` | `:binary` | Fixed length binary |
| `varbinary(n)` | `:binary` | Variable length binary |
| `tinytext` | `:string` | Small text (255 chars) |
| `text` | `:string` | Medium text (65,535 chars) |
| `mediumtext` | `:string` | Large text (16MB) |
| `longtext` | `:string` | Very large text (4GB) |
| `tinyblob` | `:binary` | Small binary (255 bytes) |
| `blob` | `:binary` | Medium binary (65KB) |
| `mediumblob` | `:binary` | Large binary (16MB) |
| `longblob` | `:binary` | Very large binary (4GB) |

### MySQL Date/Time Types

| MySQL Type | Ecto Type | Notes |
|------------|-----------|-------|
| `date` | `:date` | Date only |
| `time` | `:time` | Time only |
| `datetime` | `:naive_datetime` | Date and time |
| `timestamp` | `:utc_datetime` | Timestamp with timezone |
| `year` | `:integer` | Year only |

### MySQL Special Types

| MySQL Type | Ecto Type | Notes |
|------------|-----------|-------|
| `bit(n)` | `:integer` | Bit field |
| `enum('val1','val2',...)` | `:string` | Enumerated values |
| `set('val1','val2',...)` | `:string` | Set of values |
| `json` | `:map` | JSON data (MySQL 5.7+) |

## Implementation Recommendations

### Custom Ecto Types Needed

For full database compatibility, consider implementing custom Ecto types for:

1. **PostgreSQL-specific types**:
   - `Interval` for PostgreSQL intervals
   - `Inet` for IP addresses
   - `Point`, `Line`, `Box` etc. for geometric types
   - Range types (`Int4Range`, `DateRange`, etc.)

2. **MySQL-specific types**:
   - `Year` for MySQL YEAR type
   - `Set` for MySQL SET type
   - `Enum` for MySQL ENUM type

3. **Cross-database types**:
   - Enhanced `UUID` handling
   - `Money` type for currency

### Type Inference Strategy

1. **SQLite**: Focus on type affinity and common conventions
2. **PostgreSQL**: Use OID-based type detection from `pg_type` catalog
3. **MySQL**: Use `SHOW COLUMNS` or `INFORMATION_SCHEMA` for type detection

### Special Handling Required

- **SQLite booleans**: Detect INTEGER columns with CHECK constraints or naming conventions
- **MySQL tinyint(1)**: Special case for boolean detection
- **PostgreSQL arrays**: Parse array type syntax
- **Timezone handling**: Distinguish between naive and timezone-aware datetime types
