# Ecto Schema Metadata Extraction Research

## Overview

This document outlines the research findings for extracting field metadata from Ecto schemas to enhance schema inference in the Drops library. The goal is to programmatically access primary key, foreign key, index, and type information.

## Available Ecto.Schema `__schema__` Functions

Based on analysis of the Ecto source code (`tmp/ecto/lib/ecto/schema.ex`), the following metadata extraction functions are available:

### Primary Key Information

```elixir
# Returns a list of primary key field names (empty if none)
schema_module.__schema__(:primary_key)

# Examples:
User.__schema__(:primary_key)                    # => [:id]
CompositePrimaryKeySchema.__schema__(:primary_key) # => [:part1, :part2]
NoPrimaryKeySchema.__schema__(:primary_key)      # => []
```

### Field Information

```elixir
# Returns all non-virtual field names
schema_module.__schema__(:fields)

# Returns the type of a specific field
schema_module.__schema__(:type, field_name)

# Returns virtual field names
schema_module.__schema__(:virtual_fields)

# Returns the type of a virtual field
schema_module.__schema__(:virtual_type, field_name)

# Returns the database column name for a field (if aliased)
schema_module.__schema__(:field_source, field_name)
```

### Association Information (Foreign Keys)

```elixir
# Returns list of all association field names
schema_module.__schema__(:associations)

# Returns the association reflection struct for a specific association
schema_module.__schema__(:association, association_name)

# Association struct contains:
# - :field - association field name
# - :owner - schema module that owns the association
# - :owner_key - foreign key field in owner schema
# - :related - related schema module
# - :related_key - referenced field in related schema
# - :cardinality - :one or :many
# - :relationship - :parent or :child
```

### Other Metadata

```elixir
# Returns table/source name
schema_module.__schema__(:source)

# Returns schema prefix
schema_module.__schema__(:prefix)

# Returns list of embedded field names
schema_module.__schema__(:embeds)

# Returns embedding reflection for specific embed
schema_module.__schema__(:embed, embed_name)
```

## Foreign Key Detection Strategy

Foreign keys can be detected through two approaches:

### 1. Association Analysis (Recommended)

```elixir
# Get all associations
associations = schema_module.__schema__(:associations)

# For each association, get the reflection
for assoc_name <- associations do
  assoc = schema_module.__schema__(:association, assoc_name)

  case assoc do
    %Ecto.Association.BelongsTo{owner_key: fk_field, related_key: ref_field} ->
      # fk_field is the foreign key in this schema
      # ref_field is the referenced field in the related schema

    %Ecto.Association.HasOne{} ->
      # Foreign key is in the related schema, not this one

    %Ecto.Association.HasMany{} ->
      # Foreign key is in the related schema, not this one
  end
end
```

### 2. Field Name Pattern Analysis (Fallback)

```elixir
# Fields ending with "_id" are likely foreign keys
fields = schema_module.__schema__(:fields)
potential_fks = Enum.filter(fields, &String.ends_with?(Atom.to_string(&1), "_id"))
```

## Database Type Mapping

```elixir
# Get Ecto type for each field
for field <- schema_module.__schema__(:fields) do
  ecto_type = schema_module.__schema__(:type, field)
  # ecto_type can be: :string, :integer, :id, :binary_id, :float, etc.
end
```

## Index Information Limitations

**Critical Finding**: Ecto schemas do NOT store index information. Indices are database-level constructs that are typically defined in migrations, not in schema modules.

### Database Introspection Required

To extract index information, we need database-level introspection:

#### SQLite (Current Implementation)
```sql
-- Get index information for a table
PRAGMA index_list(table_name);
-- Returns: seq, name, unique, origin, partial

-- Get index details
PRAGMA index_info(index_name);
-- Returns: seqno, cid, name
```

#### PostgreSQL
```sql
-- Get index information
SELECT
    i.relname as index_name,
    a.attname as column_name,
    ix.indisunique as is_unique,
    ix.indisprimary as is_primary
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
WHERE t.relname = 'table_name';
```

## Implementation Strategy

### Phase 1: Ecto Schema Metadata
1. Extract primary key information using `__schema__(:primary_key)`
2. Extract foreign key information using association analysis
3. Extract field types using `__schema__(:type, field)`

### Phase 2: Database Index Introspection
1. Implement database-specific index introspection
2. Start with SQLite (current database)
3. Add PostgreSQL support later
4. Design adapter pattern for different databases

## Proposed Data Structures

```elixir
defmodule Drops.Relation.Schema.PrimaryKey do
  defstruct [:fields]

  @type t :: %__MODULE__{
    fields: [atom()]
  }
end

defmodule Drops.Relation.Schema.Index do
  defstruct [:name, :fields, :unique, :type]

  @type t :: %__MODULE__{
    name: String.t(),
    fields: [atom()],
    unique: boolean(),
    type: :btree | :hash | :gin | :gist | nil
  }
end

defmodule Drops.Relation.Schema.Indices do
  defstruct [:indices]

  @type t :: %__MODULE__{
    indices: [Drops.Relation.Schema.Index.t()]
  }
end

defmodule Drops.Relation.Schema.ForeignKey do
  defstruct [:field, :references_table, :references_field]

  @type t :: %__MODULE__{
    field: atom(),
    references_table: String.t(),
    references_field: atom()
  }
end
```

## Implementation Status

All research goals have been successfully implemented:

### ✅ Completed Features

1. **Metadata Structures**: Complete implementation of all proposed data structures
2. **Primary Key Extraction**: Full support for single, composite, and missing primary keys
3. **Foreign Key Detection**: Association analysis for `belongs_to` relationships
4. **Database Index Introspection**: SQLite and PostgreSQL support
5. **Schema Container**: `Drops.Relation.Schema` struct for complete metadata
6. **Comprehensive Testing**: 95+ tests covering all functionality

### Core Modules

#### `Drops.Relation.Schema.PrimaryKey`
```elixir
# Extract from Ecto schema
pk = Drops.Relation.Schema.PrimaryKey.from_ecto_schema(MyApp.User)

# Check properties
PrimaryKey.present?(pk)     # true if has fields
PrimaryKey.composite?(pk)   # true if multiple fields
```

#### `Drops.Relation.Schema.ForeignKey`
```elixir
# Extract all foreign keys from schema
foreign_keys = Drops.Relation.Schema.ForeignKey.from_ecto_schema(MyApp.Post)
```

#### `Drops.Relation.Schema.Index` and `Indices`
```elixir
# Create and manage indices
index = Drops.Relation.Schema.Index.new("users_email_idx", [:email], true, :btree)
indices = Drops.Relation.Schema.Indices.new([index])

# Query capabilities
email_indices = Indices.find_by_field(indices, :email)
unique_indices = Indices.unique_indices(indices)
composite_indices = Indices.composite_indices(indices)
```

#### `Drops.Relation.Schema.MetadataExtractor`
```elixir
# Extract complete metadata
metadata = MetadataExtractor.extract_metadata(MyApp.User, MyApp.Repo)

# Individual extraction methods
primary_key = MetadataExtractor.extract_primary_key(MyApp.User)
foreign_keys = MetadataExtractor.extract_foreign_keys(MyApp.User)
fields = MetadataExtractor.extract_fields(MyApp.User)
indices = MetadataExtractor.extract_indices(MyApp.User, MyApp.Repo)
```

#### `Drops.Relation.SQL.Introspector`
```elixir
# Database-specific index introspection
indices = Drops.Relation.SQL.Introspector.get_table_indices(repo, "users")
```

#### `Drops.Relation.Schema`
```elixir
# Complete schema metadata container
schema = Drops.Relation.Schema.from_ecto_schema(MyApp.User, MyApp.Repo)

# Rich query interface
Schema.primary_key_field?(schema, :id)      # true
Schema.foreign_key_field?(schema, :user_id) # true
Schema.find_field(schema, :email)           # field metadata
Schema.composite_primary_key?(schema)       # false
```

### Usage Examples

#### Basic Metadata Extraction
```elixir
# Extract complete schema metadata
schema = Drops.Relation.Schema.from_ecto_schema(MyApp.User)

# Access primary key information
IO.puts("Primary key: #{inspect(schema.primary_key.fields)}")

# Check field properties
if Schema.primary_key_field?(schema, :id) do
  IO.puts("id is the primary key")
end

# List all field names
field_names = Schema.field_names(schema)
IO.puts("Fields: #{inspect(field_names)}")
```

#### Foreign Key Analysis
```elixir
# Extract schema with associations
schema = Drops.Relation.Schema.from_ecto_schema(MyApp.Post)

# Find foreign key fields
fk_fields = Schema.foreign_key_field_names(schema)
IO.puts("Foreign keys: #{inspect(fk_fields)}")

# Get detailed foreign key information
for fk_field <- fk_fields do
  fk = Schema.get_foreign_key(schema, fk_field)
  IO.puts("#{fk.field} -> #{fk.references_table}.#{fk.references_field}")
end
```

#### Database Index Analysis
```elixir
# Extract with database introspection
schema = Drops.Relation.Schema.from_ecto_schema(MyApp.User, MyApp.Repo)

# Analyze indices
unless Indices.empty?(schema.indices) do
  IO.puts("Found #{Indices.count(schema.indices)} indices")

  # Find unique indices
  unique_indices = Indices.unique_indices(schema.indices)
  IO.puts("Unique indices: #{length(unique_indices)}")

  # Find composite indices
  composite_indices = Indices.composite_indices(schema.indices)
  IO.puts("Composite indices: #{length(composite_indices)}")

  # Check if specific field is indexed
  email_indices = Indices.find_by_field(schema.indices, :email)
  if length(email_indices) > 0 do
    IO.puts("Email field is indexed")
  end
end
```

### Test Coverage

The implementation includes comprehensive tests:

- **Primary Key Tests**: 20 tests covering single, composite, and missing primary keys
- **Foreign Key Tests**: 7 tests covering association analysis and edge cases
- **Index Tests**: 9 tests for individual index functionality
- **Indices Tests**: 15 tests for index collection management
- **Database Introspector Tests**: 5 tests for SQLite introspection
- **Metadata Extractor Tests**: 19 tests for complete metadata extraction
- **Schema Tests**: 20 tests for the main schema container

**Total: 95+ tests with 100% pass rate**

### Architecture Benefits

1. **Modular Design**: Each component has a single responsibility
2. **Database Agnostic**: Adapter pattern for different database types
3. **Rich Query Interface**: Intuitive methods for common operations
4. **Type Safety**: Comprehensive typespecs throughout
5. **Comprehensive Testing**: High test coverage with edge cases
6. **Documentation**: Extensive documentation with examples
