# Schema Caching Solutions for Drops.Relation

## Overview

Currently, Drops.Relation performs schema inference on every compilation, which involves:
1. Database introspection via SQL queries (PRAGMA for SQLite, system tables for PostgreSQL)
2. Ecto schema reflection
3. Index extraction from database
4. Schema metadata construction

This document explores caching solutions to avoid redundant schema inference when the database structure hasn't changed.

## Current Implementation Analysis

### Schema Inference Flow
```elixir
# In lib/drops/relation.ex
def infer_schema(relation, name, repo) do
  # 1. Introspect table columns and types
  columns = introspect_table_columns(repo, name)
  
  # 2. Generate Ecto schema fields
  field_definitions = generate_field_definitions(columns)
  
  # 3. Create Drops.Relation.Schema from introspected data
  drops_schema = create_drops_schema(relation, name, columns, repo)
  
  {ecto_schema, drops_schema}
end
```

### Performance Impact
- Database queries on every compilation
- Index introspection via `DatabaseIntrospector.get_table_indices/2`
- Metadata extraction via `MetadataExtractor.extract_metadata/2`

## Caching Solutions

### 1. Migration-Based Cache Invalidation (Recommended)

**Concept**: Cache schemas and invalidate only when new migrations are applied.

#### Implementation Strategy

```elixir
defmodule Drops.Relation.SchemaCache do
  @moduledoc """
  Persistent cache for inferred schemas based on migration versions.
  """
  
  @cache_file_prefix "drops_schema_cache"
  
  def get_cached_schema(repo, table_name) do
    current_version = get_migration_version(repo)
    cache_key = {repo, table_name, current_version}
    
    case read_from_cache(cache_key) do
      {:ok, schema} -> {:hit, schema}
      :error -> :miss
    end
  end
  
  def cache_schema(repo, table_name, schema) do
    current_version = get_migration_version(repo)
    cache_key = {repo, table_name, current_version}
    write_to_cache(cache_key, schema)
  end
  
  defp get_migration_version(repo) do
    # Query schema_migrations table for latest version
    query = "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1"
    case repo.query(query) do
      {:ok, %{rows: [[version]]}} -> version
      _ -> nil
    end
  end
end
```

#### Pros
- ✅ Accurate invalidation - only refreshes when schema actually changes
- ✅ Leverages existing Ecto migration infrastructure
- ✅ Works across different database adapters
- ✅ Minimal performance overhead
- ✅ Handles multiple repositories correctly

#### Cons
- ❌ Doesn't detect manual schema changes outside migrations
- ❌ Requires database query to check migration version
- ❌ Cache invalidation happens for all tables when any migration runs

### 2. File System Cache with Timestamps

**Concept**: Store cached schemas in files and use database file modification times for invalidation.

#### Implementation Strategy

```elixir
defmodule Drops.Relation.FileSystemCache do
  @cache_dir "tmp/drops_schema_cache"
  
  def get_cached_schema(repo, table_name) do
    cache_file = cache_file_path(repo, table_name)
    db_file = get_database_file_path(repo)
    
    with {:ok, cache_stat} <- File.stat(cache_file),
         {:ok, db_stat} <- File.stat(db_file),
         true <- cache_stat.mtime >= db_stat.mtime,
         {:ok, data} <- File.read(cache_file),
         {:ok, schema} <- :erlang.binary_to_term(data) do
      {:hit, schema}
    else
      _ -> :miss
    end
  end
  
  def cache_schema(repo, table_name, schema) do
    cache_file = cache_file_path(repo, table_name)
    File.mkdir_p!(Path.dirname(cache_file))
    
    data = :erlang.term_to_binary(schema)
    File.write!(cache_file, data)
  end
end
```

#### Pros
- ✅ No database queries for cache validation
- ✅ Detects any database file changes
- ✅ Simple implementation
- ✅ Works offline

#### Cons
- ❌ Database file path detection is adapter-specific
- ❌ Doesn't work with remote databases
- ❌ File modification time may not reflect schema changes in all cases
- ❌ Cache files need cleanup management

### 3. ETS + Persistent Term Hybrid Cache

**Concept**: Use ETS for fast in-memory access with persistent_term for configuration.

#### Implementation Strategy

```elixir
defmodule Drops.Relation.ETSCache do
  use GenServer
  
  @table_name :drops_schema_cache
  
  def start_link(_opts) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end
  
  def get_cached_schema(repo, table_name) do
    cache_key = {repo, table_name}
    
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, schema, version}] ->
        current_version = get_migration_version(repo)
        if version == current_version do
          {:hit, schema}
        else
          :ets.delete(@table_name, cache_key)
          :miss
        end
      [] -> :miss
    end
  end
  
  def cache_schema(repo, table_name, schema) do
    version = get_migration_version(repo)
    cache_key = {repo, table_name}
    :ets.insert(@table_name, {cache_key, schema, version})
  end
  
  def init(_) do
    :ets.new(@table_name, [:named_table, :public, :set])
    {:ok, %{}}
  end
end
```

#### Pros
- ✅ Extremely fast cache access (ETS)
- ✅ Automatic cleanup on application restart
- ✅ Memory efficient
- ✅ Built-in concurrency support

#### Cons
- ❌ Cache lost on application restart
- ❌ Still requires database queries for version checking
- ❌ Requires supervision tree setup

### 4. DETS Persistent Disk Cache

**Concept**: Use DETS (Disk ETS) for persistent caching across application restarts.

#### Implementation Strategy

```elixir
defmodule Drops.Relation.DETSCache do
  @table_name :drops_schema_cache
  @cache_file "tmp/drops_schema_cache.dets"
  
  def init_cache do
    File.mkdir_p!(Path.dirname(@cache_file))
    {:ok, _} = :dets.open_file(@table_name, [{:file, String.to_charlist(@cache_file)}])
  end
  
  def get_cached_schema(repo, table_name) do
    cache_key = {repo, table_name}
    
    case :dets.lookup(@table_name, cache_key) do
      [{^cache_key, schema, version}] ->
        current_version = get_migration_version(repo)
        if version == current_version do
          {:hit, schema}
        else
          :dets.delete(@table_name, cache_key)
          :miss
        end
      [] -> :miss
    end
  end
  
  def cache_schema(repo, table_name, schema) do
    version = get_migration_version(repo)
    cache_key = {repo, table_name}
    :dets.insert(@table_name, {cache_key, schema, version})
  end
  
  def close_cache do
    :dets.close(@table_name)
  end
end
```

#### Pros
- ✅ Persistent across application restarts
- ✅ Built-in Erlang/OTP solution
- ✅ Atomic operations
- ✅ Handles concurrent access

#### Cons
- ❌ Slower than ETS (disk I/O)
- ❌ DETS file corruption possible
- ❌ Limited scalability
- ❌ Requires explicit file management

## Comparison Matrix

| Solution | Persistence | Performance | Accuracy | Complexity | Database Queries |
|----------|-------------|-------------|----------|------------|------------------|
| Migration-based | File/ETS | High | High | Medium | Minimal |
| File System | File | Medium | Medium | Low | None |
| ETS Hybrid | Memory | Very High | High | Medium | Minimal |
| DETS | Disk | Medium | High | Medium | Minimal |

## Recommended Implementation

### Phase 1: Migration-Based Cache with ETS

Combine migration-based invalidation with ETS for optimal performance:

```elixir
defmodule Drops.Relation.SchemaCache do
  @ets_table :drops_schema_cache
  
  def get_or_infer_schema(repo, table_name, infer_fn) do
    case get_cached_schema(repo, table_name) do
      {:hit, schema} -> schema
      :miss -> 
        schema = infer_fn.()
        cache_schema(repo, table_name, schema)
        schema
    end
  end
  
  defp get_cached_schema(repo, table_name) do
    current_version = get_migration_version(repo)
    cache_key = {repo, table_name}
    
    case :ets.lookup(@ets_table, cache_key) do
      [{^cache_key, schema, ^current_version}] -> {:hit, schema}
      _ -> :miss
    end
  end
  
  defp cache_schema(repo, table_name, schema) do
    version = get_migration_version(repo)
    cache_key = {repo, table_name}
    :ets.insert(@ets_table, {cache_key, schema, version})
  end
end
```

### Phase 2: Optional File Persistence

Add DETS backing for persistence across restarts:

```elixir
# In application.ex
def start(_type, _args) do
  children = [
    Drops.Relation.SchemaCache,
    # ... other children
  ]
  
  Supervisor.start_link(children, strategy: :one_for_one)
end
```

## Integration Points

### 1. Modify `Drops.Relation.infer_schema/3`

```elixir
def infer_schema(relation, name, repo) do
  Drops.Relation.SchemaCache.get_or_infer_schema(repo, name, fn ->
    # Existing inference logic
    columns = introspect_table_columns(repo, name)
    field_definitions = generate_field_definitions(columns)
    drops_schema = create_drops_schema(relation, name, columns, repo)
    {ecto_schema, drops_schema}
  end)
end
```

### 2. Cache Invalidation Hooks

```elixir
# Optional: Hook into Ecto.Migrator for automatic cache clearing
defmodule Drops.Relation.MigrationHooks do
  def after_migration(repo, _direction, _version) do
    Drops.Relation.SchemaCache.clear_repo_cache(repo)
  end
end
```

## Configuration Options

```elixir
# config/config.exs
config :drops, :schema_cache,
  enabled: true,
  backend: :ets, # :ets, :dets, :file
  ttl: :infinity, # or time in milliseconds
  cache_dir: "tmp/drops_cache"
```

## Conclusion

The **Migration-Based Cache with ETS** approach provides the best balance of performance, accuracy, and implementation complexity. It leverages Ecto's existing migration tracking infrastructure while providing fast in-memory access to cached schemas.

This solution ensures schemas are only re-inferred when the database structure actually changes, significantly reducing compilation time for applications with many Drops.Relation modules.
