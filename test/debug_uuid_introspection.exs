alias Drops.Relation.SQL.Introspector

# Check what the introspector returns for the uuid_organizations table
columns = Introspector.introspect_table_columns(Drops.Repos.Sqlite, "uuid_organizations")

IO.puts("Columns for uuid_organizations:")
Enum.each(columns, fn column ->
  IO.puts("  #{column.name}: #{column.type} (primary_key: #{column.primary_key})")
  
  # Check what the introspector converts this to
  ecto_type = Introspector.db_type_to_ecto_type(Drops.Repos.Sqlite, column.type, column.name)
  IO.puts("    -> ecto_type: #{inspect(ecto_type)}")
end)
