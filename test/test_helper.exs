Code.require_file("test/support/setup.ex")

Drops.Repos.each_repo(fn repo -> Drops.Relation.SchemaCache.refresh(repo) end)

Code.require_file("support/test_config.ex", __DIR__)
Code.require_file("support/doctest_case.ex", __DIR__)
Code.require_file("support/data_case.ex", __DIR__)
Code.require_file("support/contract_case.ex", __DIR__)
Code.require_file("support/operation_case.ex", __DIR__)
Code.require_file("support/relation_case.ex", __DIR__)
