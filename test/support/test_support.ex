if Mix.env() in [:dev, :test] do
  # Ensure test repo is loaded at compile time
  Code.require_file("test/support/test_repo.ex")

  defmodule Drops.TestSupport do
    @moduledoc """
    Module that ensures test support files are loaded in dev and test environments.
    """

    def ensure_test_repo_loaded do
      case Code.ensure_compiled(Drops.TestRepo) do
        {:module, _} ->
          :ok

        {:error, _} ->
          # Try to require the file
          try do
            Code.require_file("test/support/test_repo.ex")
            :ok
          rescue
            _ -> :error
          end
      end
    end
  end
end
