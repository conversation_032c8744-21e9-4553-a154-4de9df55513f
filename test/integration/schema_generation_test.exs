defmodule Integration.SchemaGenerationTest do
  use ExUnit.Case, async: false

  import ExUnit.CaptureIO

  @sample_app_dir Path.join(File.cwd!(), "test/sample_app")
  @test_schemas_dir Path.join(File.cwd!(), "test/sample_app/lib/sample_app/relations")

  setup_all do
    # Set up the sample app environment
    original_dir = File.cwd!()

    # Change to sample app directory
    File.cd!(@sample_app_dir)

    # Install dependencies
    System.cmd("mix", ["deps.get"], stderr_to_stdout: true)

    # Create and migrate database
    System.cmd("mix", ["ecto.create"], stderr_to_stdout: true)
    System.cmd("mix", ["ecto.migrate"], stderr_to_stdout: true)

    on_exit(fn ->
      # Clean up
      File.cd!(original_dir)

      # Remove generated schemas
      if File.exists?(@test_schemas_dir) do
        File.rm_rf!(@test_schemas_dir)
      end

      # Remove database
      db_path = Path.join(@sample_app_dir, "priv/db.sqlite")

      if File.exists?(db_path) do
        File.rm!(db_path)
      end
    end)

    :ok
  end

  describe "complete schema generation workflow" do
    setup do
      # Clean up database files to avoid locks
      File.cd!(@sample_app_dir)

      System.cmd(
        "rm",
        ["-f", "priv/db.sqlite", "priv/db.sqlite-shm", "priv/db.sqlite-wal"],
        stderr_to_stdout: true
      )

      # Clean up any existing schemas
      if File.exists?(@test_schemas_dir) do
        File.rm_rf!(@test_schemas_dir)
      end

      # Recreate database from scratch
      System.cmd("mix", ["ecto.drop"], stderr_to_stdout: true)
      System.cmd("mix", ["ecto.create"], stderr_to_stdout: true)
      System.cmd("mix", ["ecto.migrate"], stderr_to_stdout: true)

      # Small delay to ensure database is ready
      Process.sleep(100)
      :ok
    end

    test "generates schemas for all tables" do
      # Change to sample app directory for the test
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)

      try do
        # Run the schema generation task
        _output =
          capture_io(fn ->
            System.cmd(
              "mix",
              [
                "drops.relations.gen_schemas",
                "--app",
                "SampleApp",
                "--namespace",
                "SampleApp.Relations",
                "--yes"
              ],
              stderr_to_stdout: true
            )
          end)

        # Check that schemas were generated
        user_schema_path = Path.join(@test_schemas_dir, "users.ex")
        post_schema_path = Path.join(@test_schemas_dir, "posts.ex")
        comment_schema_path = Path.join(@test_schemas_dir, "comments.ex")

        assert File.exists?(user_schema_path), "User schema should be generated"
        assert File.exists?(post_schema_path), "Post schema should be generated"
        assert File.exists?(comment_schema_path), "Comment schema should be generated"

        # Verify user schema content
        user_content = File.read!(user_schema_path)
        assert user_content =~ "defmodule SampleApp.Relations.Users do"
        assert user_content =~ "use Ecto.Schema"
        assert user_content =~ ~s(schema "users" do)
        assert user_content =~ "field(:email, :string)"
        assert user_content =~ "field(:first_name, :string)"
        assert user_content =~ "field(:last_name, :string)"
        assert user_content =~ "field(:age, :integer)"
        assert user_content =~ "field(:is_active, :integer)"
        assert user_content =~ "timestamps()"

        # Verify post schema content
        post_content = File.read!(post_schema_path)
        assert post_content =~ "defmodule SampleApp.Relations.Posts do"
        assert post_content =~ ~s(schema "posts" do)
        assert post_content =~ "field(:title, :string)"
        assert post_content =~ "field(:body, :string)"
        assert post_content =~ "field(:published, :integer)"
        assert post_content =~ "field(:user_id, :id)"

        # Verify comment schema content
        comment_content = File.read!(comment_schema_path)
        assert comment_content =~ "defmodule SampleApp.Relations.Comments do"
        assert comment_content =~ ~s(schema "comments" do)
        assert comment_content =~ "field(:body, :string)"
        assert comment_content =~ "field(:approved, :integer)"
        assert comment_content =~ "field(:user_id, :id)"
        assert comment_content =~ "field(:post_id, :id)"
      after
        File.cd!(original_dir)
      end
    end

    test "generates schemas for specific tables only" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)

      try do
        # Clean up any existing schemas
        if File.exists?(@test_schemas_dir) do
          File.rm_rf!(@test_schemas_dir)
        end

        # Run the schema generation task for users table only
        capture_io(fn ->
          System.cmd(
            "mix",
            [
              "drops.relations.gen_schemas",
              "--app",
              "SampleApp",
              "--tables",
              "users",
              "--namespace",
              "SampleApp.Relations",
              "--yes"
            ],
            stderr_to_stdout: true
          )
        end)

        # Check that only user schema was generated
        user_schema_path = Path.join(@test_schemas_dir, "users.ex")
        post_schema_path = Path.join(@test_schemas_dir, "posts.ex")
        comment_schema_path = Path.join(@test_schemas_dir, "comments.ex")

        assert File.exists?(user_schema_path), "User schema should be generated"
        refute File.exists?(post_schema_path), "Post schema should not be generated"
        refute File.exists?(comment_schema_path), "Comment schema should not be generated"
      after
        File.cd!(original_dir)
      end
    end

    test "handles sync mode correctly" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)

      try do
        # First, generate schemas
        capture_io(fn ->
          System.cmd(
            "mix",
            [
              "drops.relations.gen_schemas",
              "--app",
              "SampleApp",
              "--tables",
              "users",
              "--namespace",
              "SampleApp.Relations",
              "--yes"
            ],
            stderr_to_stdout: true
          )
        end)

        user_schema_path = Path.join(@test_schemas_dir, "users.ex")
        assert File.exists?(user_schema_path)

        # Modify the file to add a custom comment
        original_content = File.read!(user_schema_path)

        modified_content =
          String.replace(
            original_content,
            "use Ecto.Schema",
            "use Ecto.Schema\n  # Custom comment"
          )

        File.write!(user_schema_path, modified_content)

        # Run generation again with sync=true (default)
        capture_io(fn ->
          System.cmd(
            "mix",
            [
              "drops.relations.gen_schemas",
              "--app",
              "SampleApp",
              "--tables",
              "users",
              "--namespace",
              "SampleApp.Relations",
              "--sync",
              "true",
              "--yes"
            ],
            stderr_to_stdout: true
          )
        end)

        # File should still exist and be updated
        assert File.exists?(user_schema_path)

        # Run generation with sync=false
        capture_io(fn ->
          System.cmd(
            "mix",
            [
              "drops.relations.gen_schemas",
              "--app",
              "SampleApp",
              "--tables",
              "users",
              "--namespace",
              "SampleApp.Relations",
              "--sync",
              "false",
              "--yes"
            ],
            stderr_to_stdout: true
          )
        end)

        # File should be overwritten
        final_content = File.read!(user_schema_path)
        assert final_content =~ "defmodule SampleApp.Relations.Users do"
      after
        File.cd!(original_dir)
      end
    end

    test "handles custom directory and namespace" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)

      custom_dir = "lib/sample_app/schemas"

      try do
        # Clean up any existing files
        if File.exists?(custom_dir) do
          File.rm_rf!(custom_dir)
        end

        # Run with custom namespace and directory
        capture_io(fn ->
          System.cmd(
            "mix",
            [
              "drops.relations.gen_schemas",
              "--app",
              "SampleApp",
              "--tables",
              "users",
              "--namespace",
              "SampleApp.Schemas",
              "--dir",
              custom_dir,
              "--yes"
            ],
            stderr_to_stdout: true
          )
        end)

        # Check that schema was generated in custom location
        user_schema_path = Path.join(custom_dir, "users.ex")

        assert File.exists?(user_schema_path),
               "User schema should be generated in custom directory"

        # Verify content has correct namespace
        user_content = File.read!(user_schema_path)
        assert user_content =~ "defmodule SampleApp.Schemas.Users do"
      after
        # Clean up custom directory
        if File.exists?(custom_dir) do
          File.rm_rf!(custom_dir)
        end

        File.cd!(original_dir)
      end
    end
  end

  describe "error handling" do
    setup do
      # Ensure no database exists for error testing
      File.cd!(@sample_app_dir)

      System.cmd(
        "rm",
        ["-f", "priv/db.sqlite", "priv/db.sqlite-shm", "priv/db.sqlite-wal"],
        stderr_to_stdout: true
      )

      :ok
    end

    test "handles missing database gracefully" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)

      try do
        # Remove database
        db_path = "priv/db.sqlite"

        if File.exists?(db_path) do
          File.rm!(db_path)
        end

        # Try to generate schemas
        {output, _exit_code} =
          System.cmd(
            "mix",
            [
              "drops.relations.gen_schemas",
              "--app",
              "SampleApp",
              "--yes"
            ],
            stderr_to_stdout: true
          )

        # Should handle error gracefully
        assert output =~ "No tables found to generate schemas for" or
                 output =~ "Failed to introspect" or
                 output =~ "Failed to start application"
      after
        File.cd!(original_dir)
      end
    end
  end
end
