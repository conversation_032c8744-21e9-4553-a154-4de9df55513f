defmodule Drops.Relation.SchemaCachePersistenceTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.SchemaCache
  alias Drops.Config

  # Mock repository for testing
  defmodule TestRepo do
    def config do
      [priv: "test/fixtures/persistence_repo"]
    end
  end

  describe "JSON file persistence" do
    test "cache persists across application restarts" do
      # Enable cache for tests
      original_config = Config.schema_cache()

      on_exit(fn ->
        Config.update(:schema_cache, original_config)
        File.rm_rf!("test/fixtures")
      end)

      Config.update(:schema_cache, enabled: true)

      # Create test fixture directories
      File.mkdir_p!("test/fixtures/persistence_repo/migrations")

      File.write!(
        "test/fixtures/persistence_repo/migrations/001_create_users.exs",
        "# migration 1"
      )

      # Clear cache to start fresh
      SchemaCache.clear_all()

      # Create a test schema to cache
      test_schema = %Drops.Relation.Schema{
        source: "users",
        primary_key: nil,
        foreign_keys: [],
        fields: [],
        indices: %Drops.Relation.Schema.Indices{indices: []},
        virtual_fields: []
      }

      SchemaCache.cache_schema(TestRepo, "users", test_schema)

      # Verify it's cached
      result1 = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result1.source == test_schema.source

      # Simulate application restart by clearing in-memory state and reading from disk
      # The cache should still contain our entry (persistence test)
      result2 = SchemaCache.get_cached_schema(TestRepo, "users")

      # If persistence works, we should get the original cached value
      assert result2.source == test_schema.source

      # Clean up
      SchemaCache.clear_all()
    end

    test "cache handles JSON file corruption gracefully" do
      # This test ensures the system doesn't crash if JSON file is corrupted
      original_config = Config.schema_cache()

      on_exit(fn ->
        Config.update(:schema_cache, original_config)
        File.rm_rf!("test/fixtures")
      end)

      Config.update(:schema_cache, enabled: true)

      # Create test fixture directories
      File.mkdir_p!("test/fixtures/persistence_repo/migrations")

      File.write!(
        "test/fixtures/persistence_repo/migrations/001_create_users.exs",
        "# migration 1"
      )

      # Basic functionality should still work
      test_schema = %Drops.Relation.Schema{
        source: "test_table",
        primary_key: nil,
        foreign_keys: [],
        fields: [],
        indices: %Drops.Relation.Schema.Indices{indices: []},
        virtual_fields: []
      }

      SchemaCache.cache_schema(TestRepo, "test_table", test_schema)

      result = SchemaCache.get_cached_schema(TestRepo, "test_table")
      assert result.source == test_schema.source
    end
  end
end
