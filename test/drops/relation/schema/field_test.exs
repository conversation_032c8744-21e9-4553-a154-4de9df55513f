defmodule Drops.Relation.Schema.FieldTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema.Field

  describe "Field.new/5" do
    test "creates a field with basic information" do
      field = Field.new(:email, :string, :string, :email)
      
      assert field.name == :email
      assert field.type == :string
      assert field.ecto_type == :string
      assert field.source == :email
      assert field.meta == %{}
    end

    test "creates a field with metadata" do
      meta = %{is_nullable: false, default: "active", check_constraints: ["status IN ('active', 'inactive')"]}
      field = Field.new(:status, :string, :string, :status, meta)
      
      assert field.name == :status
      assert field.type == :string
      assert field.ecto_type == :string
      assert field.source == :status
      assert field.meta == meta
    end

    test "creates a field with parameterized type" do
      ecto_type = {Ecto.Enum, values: [:red, :green, :blue]}
      field = Field.new(:tags, :string, ecto_type, :tags)
      
      assert field.name == :tags
      assert field.type == :string
      assert field.ecto_type == ecto_type
      assert field.source == :tags
    end
  end

  describe "Field.merge/2" do
    test "merges two fields with same name" do
      inferred = Field.new(:email, :string, :string, :email, %{is_nullable: true, default: nil})
      custom = Field.new(:email, :string, {:parameterized, {Ecto.Enum, %{values: [:active, :inactive]}}}, :email, %{default: "active"})
      
      merged = Field.merge(inferred, custom)
      
      assert merged.name == :email
      assert merged.type == :string
      assert merged.ecto_type == {:parameterized, {Ecto.Enum, %{values: [:active, :inactive]}}}
      assert merged.source == :email
      assert merged.meta.is_nullable == true  # from inferred
      assert merged.meta.default == "active"  # from custom (takes precedence)
    end

    test "custom field metadata takes precedence over inferred" do
      inferred = Field.new(:status, :string, :string, :status, %{is_nullable: true, default: nil})
      custom = Field.new(:status, :string, :string, :status, %{is_nullable: false, default: "active"})
      
      merged = Field.merge(inferred, custom)
      
      assert merged.meta.is_nullable == false
      assert merged.meta.default == "active"
    end

    test "preserves inferred metadata when custom doesn't override" do
      inferred = Field.new(:email, :string, :string, :email, %{is_nullable: true, check_constraints: ["email LIKE '%@%'"]})
      custom = Field.new(:email, :string, :string, :email, %{default: "<EMAIL>"})
      
      merged = Field.merge(inferred, custom)
      
      assert merged.meta.is_nullable == true
      assert merged.meta.check_constraints == ["email LIKE '%@%'"]
      assert merged.meta.default == "<EMAIL>"
    end

    test "raises error when merging fields with different names" do
      field1 = Field.new(:email, :string, :string, :email)
      field2 = Field.new(:name, :string, :string, :name)
      
      assert_raise ArgumentError, ~r/Cannot merge fields with different names/, fn ->
        Field.merge(field1, field2)
      end
    end
  end

  describe "Field.from_metadata/1" do
    test "creates field from metadata map" do
      metadata = %{
        name: :email,
        type: :string,
        ecto_type: :string,
        source: :email,
        meta: %{is_nullable: false}
      }
      
      field = Field.from_metadata(metadata)
      
      assert field.name == :email
      assert field.type == :string
      assert field.ecto_type == :string
      assert field.source == :email
      assert field.meta == %{is_nullable: false}
    end

    test "creates field from metadata map without meta" do
      metadata = %{
        name: :email,
        type: :string,
        ecto_type: :string,
        source: :email
      }
      
      field = Field.from_metadata(metadata)
      
      assert field.name == :email
      assert field.meta == %{}
    end
  end

  describe "Field.to_metadata/1" do
    test "converts field to metadata map" do
      meta = %{is_nullable: false, default: "active"}
      field = Field.new(:status, :string, :string, :status, meta)
      
      metadata = Field.to_metadata(field)
      
      assert metadata == %{
        name: :status,
        type: :string,
        ecto_type: :string,
        source: :status,
        meta: meta
      }
    end
  end
end
