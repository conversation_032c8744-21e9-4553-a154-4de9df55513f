defmodule Drops.Relation.Schema.ForeignKeyTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema.ForeignKey
  alias Test.Ecto.TestSchemas

  describe "new/4" do
    test "creates foreign key with all parameters" do
      fk = ForeignKey.new(:user_id, "users", :id, :user)

      assert fk.field == :user_id
      assert fk.references_table == "users"
      assert fk.references_field == :id
      assert fk.association_name == :user
    end

    test "creates foreign key without association name" do
      fk = ForeignKey.new(:user_id, "users", :id)

      assert fk.field == :user_id
      assert fk.references_table == "users"
      assert fk.references_field == :id
      assert fk.association_name == nil
    end
  end

  describe "from_ecto_schema/1" do
    test "extracts foreign keys from schema with belongs_to associations" do
      foreign_keys = ForeignKey.from_ecto_schema(TestSchemas.AssociationsSchema)

      assert length(foreign_keys) == 1

      fk = hd(foreign_keys)
      assert fk.field == :parent_id
      assert fk.references_table == "association_parents"
      assert fk.references_field == :id
      assert fk.association_name == :parent
    end

    test "extracts foreign keys from association item schema" do
      foreign_keys = ForeignKey.from_ecto_schema(TestSchemas.AssociationItemSchema)

      assert length(foreign_keys) == 1

      fk = hd(foreign_keys)
      assert fk.field == :association_id
      assert fk.references_table == "associations"
      assert fk.references_field == :id
      assert fk.association_name == :association
    end

    test "returns empty list for schema without belongs_to associations" do
      foreign_keys = ForeignKey.from_ecto_schema(TestSchemas.UserSchema)

      assert foreign_keys == []
    end

    test "ignores has_many and has_one associations" do
      # AssociationParentSchema has has_many but no belongs_to
      foreign_keys = ForeignKey.from_ecto_schema(TestSchemas.AssociationParentSchema)

      assert foreign_keys == []
    end

    test "returns empty list for schema with no associations" do
      foreign_keys = ForeignKey.from_ecto_schema(TestSchemas.UserSchema)

      assert foreign_keys == []
    end
  end
end
