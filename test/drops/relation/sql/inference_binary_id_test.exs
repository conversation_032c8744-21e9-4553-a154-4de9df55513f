defmodule Drops.Relation.SQL.InferenceBinaryIdTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.SQL.Inference

  # Helper function that mirrors the logic in create_field_from_column
  defp determine_ecto_type(column, base_ecto_type) do
    cond do
      column.primary_key and base_ecto_type == :integer ->
        :id

      column.primary_key and base_ecto_type == :binary_id ->
        :binary_id

      String.ends_with?(column.name, "_id") and base_ecto_type == :integer ->
        :id

      String.ends_with?(column.name, "_id") and base_ecto_type == :binary_id ->
        :binary_id

      true ->
        base_ecto_type
    end
  end

  describe "binary_id field inference" do
    test "normalize_ecto_type handles binary_id correctly" do
      assert Inference.normalize_ecto_type(:binary_id) == :binary
      assert Inference.normalize_ecto_type(:id) == :integer
      assert Inference.normalize_ecto_type(:string) == :string
      assert Inference.normalize_ecto_type({:array, :binary_id}) == {:array, :binary}
    end

    test "field creation logic handles binary_id primary key correctly" do
      # Test binary_id primary key scenario
      id_column = %{name: "id", type: "uuid", primary_key: true, not_null: true}
      base_ecto_type = :binary_id

      ecto_type = determine_ecto_type(id_column, base_ecto_type)

      assert ecto_type == :binary_id
      assert Inference.normalize_ecto_type(ecto_type) == :binary
    end

    test "field creation logic handles integer primary key correctly" do
      # Test integer primary key scenario
      id_column = %{name: "id", type: "integer", primary_key: true, not_null: true}
      base_ecto_type = :integer

      ecto_type = determine_ecto_type(id_column, base_ecto_type)

      assert ecto_type == :id
      assert Inference.normalize_ecto_type(ecto_type) == :integer
    end

    test "field creation logic handles binary_id foreign keys correctly" do
      # Test binary_id foreign key scenario
      user_id_column = %{
        name: "user_id",
        type: "uuid",
        primary_key: false,
        not_null: true
      }

      base_ecto_type = :binary_id
      ecto_type = determine_ecto_type(user_id_column, base_ecto_type)

      assert ecto_type == :binary_id
      assert Inference.normalize_ecto_type(ecto_type) == :binary
    end

    test "field creation logic handles integer foreign keys correctly" do
      # Test integer foreign key scenario
      user_id_column = %{
        name: "user_id",
        type: "integer",
        primary_key: false,
        not_null: true
      }

      base_ecto_type = :integer
      ecto_type = determine_ecto_type(user_id_column, base_ecto_type)

      assert ecto_type == :id
      assert Inference.normalize_ecto_type(ecto_type) == :integer
    end

    test "field creation logic handles regular fields correctly" do
      # Test regular field scenario (not primary key, not foreign key)
      name_column = %{
        name: "name",
        type: "text",
        primary_key: false,
        not_null: true
      }

      base_ecto_type = :string
      ecto_type = determine_ecto_type(name_column, base_ecto_type)

      assert ecto_type == :string
      assert Inference.normalize_ecto_type(ecto_type) == :string
    end
  end
end
