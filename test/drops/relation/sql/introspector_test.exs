defmodule Drops.Relation.SQL.IntrospectorTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.SQL.Introspector
  alias Drops.Relation.Schema.{Indices, Index}

  describe "get_table_indices/2" do
    @tag relations: [:users]
    test "extracts indices from SQLite database", %{repo: repo} do
      # Test index extraction using the users table which has indices
      {:ok, indices} = Introspector.get_table_indices(repo, "users")

      assert %Indices{} = indices
      # Should have at least the indices we created in migration
      assert length(indices.indices) >= 3

      # Find specific indices (names may vary by adapter)
      email_index = Enum.find(indices.indices, &(:email in Index.field_names(&1)))
      name_index = Enum.find(indices.indices, &(Index.field_names(&1) == [:name]))

      composite_index =
        Enum.find(indices.indices, &(Index.field_names(&1) == [:name, :age]))

      assert email_index != nil
      assert Index.field_names(email_index) == [:email]
      assert email_index.unique

      assert name_index != nil
      assert Index.field_names(name_index) == [:name]
      refute name_index.unique

      assert composite_index != nil
      assert Index.field_names(composite_index) == [:name, :age]
      refute composite_index.unique
    end

    @tag relations: [:simple_data]
    test "handles table with no custom indices", %{repo: repo} do
      {:ok, indices} = Introspector.get_table_indices(repo, "simple_data")

      assert %Indices{} = indices
      # Should have minimal or no indices (SQLite may create automatic indices)
    end

    test "handles non-existent table" do
      result =
        Introspector.get_table_indices(Drops.Repos.Sqlite, "non_existent_table")

      # SQLite doesn't error on non-existent tables for PRAGMA, just returns empty
      case result do
        {:ok, indices} ->
          assert %Indices{} = indices
          assert indices.indices == []

        {:error, _} ->
          # Some adapters might return an error
          assert true
      end
    end
  end

  describe "get_sqlite_indices/2" do
    @tag relations: [:users]
    test "extracts SQLite indices with correct metadata", %{repo: repo} do
      # Use the users table which has various index types
      {:ok, indices} = Introspector.get_table_indices(repo, "users")

      assert %Indices{} = indices

      # Find the unique email index
      email_index =
        Enum.find(indices.indices, &(:email in Index.field_names(&1) and &1.unique))

      assert email_index != nil
      assert Index.field_names(email_index) == [:email]
      assert email_index.unique == true
      assert email_index.type == :btree

      # Find the name index
      name_index = Enum.find(indices.indices, &(Index.field_names(&1) == [:name]))
      assert name_index != nil
      assert Index.field_names(name_index) == [:name]
      assert name_index.unique == false

      # Find the composite index
      composite_index =
        Enum.find(indices.indices, &(Index.field_names(&1) == [:name, :age]))

      assert composite_index != nil
      assert Index.field_names(composite_index) == [:name, :age]
      assert composite_index.unique == false
    end
  end

  describe "adapter detection" do
    @tag relations: [:users]
    test "detects SQLite adapter correctly", %{repo: repo} do
      # This is an internal test - we know we're using SQLite in tests
      assert Introspector.get_table_indices(repo, "users") |> elem(0) == :ok
    end
  end
end
