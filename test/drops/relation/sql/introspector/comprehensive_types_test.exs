defmodule Drops.Relation.SQL.Introspector.ComprehensiveTypesTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.SQL.Introspector

  describe "SQLite type mapping" do
    @tag relations: [:type_mapping_tests]
    test "correctly maps SQLite core types", %{repo: repo} do
      columns = Introspector.introspect_table_columns(repo, "type_mapping_tests")

      # Find specific columns and verify their type mapping
      integer_col = find_column(columns, "integer_type")
      assert integer_col.type == "INTEGER"

      assert Introspector.db_type_to_ecto_type(repo, integer_col.type, integer_col.name) ==
               :integer

      real_col = find_column(columns, "real_type")
      assert real_col.type == "REAL"

      assert Introspector.db_type_to_ecto_type(repo, real_col.type, real_col.name) ==
               :float

      text_col = find_column(columns, "text_type")
      assert text_col.type == "TEXT"

      assert Introspector.db_type_to_ecto_type(repo, text_col.type, text_col.name) ==
               :string

      blob_col = find_column(columns, "blob_type")
      assert blob_col.type == "BLOB"

      assert Introspector.db_type_to_ecto_type(repo, blob_col.type, blob_col.name) ==
               :binary
    end

    @tag relations: [:type_mapping_tests]
    test "correctly maps SQLite interpreted types", %{repo: repo} do
      columns = Introspector.introspect_table_columns(repo, "type_mapping_tests")

      # Test NUMERIC type
      numeric_col = find_column(columns, "numeric_type")

      assert Introspector.db_type_to_ecto_type(repo, "NUMERIC", numeric_col.name) ==
               :decimal

      # Test boolean type (stored as INTEGER)
      boolean_col = find_column(columns, "boolean_type")

      assert Introspector.db_type_to_ecto_type(repo, "BOOLEAN", boolean_col.name) ==
               :boolean

      # Test date/time types
      date_col = find_column(columns, "date_type")
      assert Introspector.db_type_to_ecto_type(repo, "DATE", date_col.name) == :date

      datetime_col = find_column(columns, "datetime_type")

      assert Introspector.db_type_to_ecto_type(repo, "DATETIME", datetime_col.name) ==
               :naive_datetime

      timestamp_col = find_column(columns, "timestamp_type")

      assert Introspector.db_type_to_ecto_type(repo, "TIMESTAMP", timestamp_col.name) ==
               :naive_datetime

      time_col = find_column(columns, "time_type")
      assert Introspector.db_type_to_ecto_type(repo, "TIME", time_col.name) == :time
    end

    @tag relations: [:type_mapping_tests]
    test "correctly maps SQLite parameterized types", %{repo: repo} do
      # Test parameterized types
      assert Introspector.db_type_to_ecto_type(repo, "DECIMAL(10,2)", "price") == :decimal

      assert Introspector.db_type_to_ecto_type(repo, "NUMERIC(15,4)", "amount") ==
               :decimal

      assert Introspector.db_type_to_ecto_type(repo, "VARCHAR(255)", "name") == :string
      assert Introspector.db_type_to_ecto_type(repo, "CHAR(10)", "code") == :string
    end

    @tag relations: [:type_mapping_tests]
    test "correctly maps additional SQLite types", %{repo: repo} do
      # Test additional interpreted types
      assert Introspector.db_type_to_ecto_type(repo, "FLOAT", "value") == :float
      assert Introspector.db_type_to_ecto_type(repo, "DOUBLE", "value") == :float
      assert Introspector.db_type_to_ecto_type(repo, "CLOB", "content") == :string
      assert Introspector.db_type_to_ecto_type(repo, "JSON", "metadata") == :map
    end

    @tag relations: [:type_mapping_tests]
    test "maps INTEGER fields consistently to integer type", %{repo: repo} do
      # All INTEGER fields should map to :integer regardless of naming
      field_names = [
        "is_active",
        "has_permission",
        "can_edit",
        "enabled_flag",
        "is_visible",
        "user_id",
        "count",
        "id"
      ]

      Enum.each(field_names, fn field_name ->
        assert Introspector.db_type_to_ecto_type(repo, "INTEGER", field_name) == :integer,
               "Field #{field_name} should map to :integer"
      end)
    end
  end

  describe "PostgreSQL type mapping" do
    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL integer types and aliases", %{repo: repo} do
      # Test integer types
      assert Introspector.db_type_to_ecto_type(repo, "integer", "id") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "int", "count") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "int4", "value") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "bigint", "big_id") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "int8", "big_value") == :integer

      assert Introspector.db_type_to_ecto_type(repo, "smallint", "small_value") ==
               :integer

      assert Introspector.db_type_to_ecto_type(repo, "int2", "tiny_value") == :integer
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL floating point types", %{repo: repo} do
      # Test floating point types
      assert Introspector.db_type_to_ecto_type(repo, "real", "price") == :float
      assert Introspector.db_type_to_ecto_type(repo, "float4", "rate") == :float

      assert Introspector.db_type_to_ecto_type(repo, "double precision", "precise_value") ==
               :float

      assert Introspector.db_type_to_ecto_type(repo, "float8", "big_float") == :float
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL decimal and money types", %{repo: repo} do
      # Test decimal types
      assert Introspector.db_type_to_ecto_type(repo, "numeric", "amount") == :decimal
      assert Introspector.db_type_to_ecto_type(repo, "decimal", "price") == :decimal
      assert Introspector.db_type_to_ecto_type(repo, "money", "salary") == :decimal

      assert Introspector.db_type_to_ecto_type(repo, "numeric(10,2)", "precise_amount") ==
               :decimal
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL string types", %{repo: repo} do
      # Test string types
      assert Introspector.db_type_to_ecto_type(repo, "text", "description") == :string

      assert Introspector.db_type_to_ecto_type(repo, "character varying", "name") ==
               :string

      assert Introspector.db_type_to_ecto_type(repo, "varchar", "title") == :string
      assert Introspector.db_type_to_ecto_type(repo, "character", "code") == :string
      assert Introspector.db_type_to_ecto_type(repo, "char", "status") == :string
      assert Introspector.db_type_to_ecto_type(repo, "name", "pg_name") == :string

      assert Introspector.db_type_to_ecto_type(repo, "varchar(255)", "limited_text") ==
               :string
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL date/time types", %{repo: repo} do
      # Test date/time types
      assert Introspector.db_type_to_ecto_type(repo, "date", "birth_date") == :date

      assert Introspector.db_type_to_ecto_type(
               repo,
               "time without time zone",
               "start_time"
             ) == :time

      assert Introspector.db_type_to_ecto_type(repo, "time with time zone", "tz_time") ==
               :time

      assert Introspector.db_type_to_ecto_type(repo, "timetz", "short_tz_time") == :time

      assert Introspector.db_type_to_ecto_type(
               repo,
               "timestamp without time zone",
               "created_at"
             ) == :naive_datetime

      assert Introspector.db_type_to_ecto_type(repo, "timestamp", "updated_at") ==
               :naive_datetime

      assert Introspector.db_type_to_ecto_type(
               repo,
               "timestamp with time zone",
               "tz_timestamp"
             ) == :utc_datetime

      assert Introspector.db_type_to_ecto_type(repo, "timestamptz", "short_tz_timestamp") ==
               :utc_datetime
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL special types", %{repo: repo} do
      # Test special types
      assert Introspector.db_type_to_ecto_type(repo, "boolean", "is_active") == :boolean
      assert Introspector.db_type_to_ecto_type(repo, "bytea", "binary_data") == :binary
      assert Introspector.db_type_to_ecto_type(repo, "json", "metadata") == :map
      assert Introspector.db_type_to_ecto_type(repo, "jsonb", "structured_data") == :map
      assert Introspector.db_type_to_ecto_type(repo, "uuid", "unique_id") == :binary_id
      assert Introspector.db_type_to_ecto_type(repo, "xml", "document") == :string
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL network types", %{repo: repo} do
      # Test network types (mapped to string for now)
      assert Introspector.db_type_to_ecto_type(repo, "inet", "ip_address") == :string
      assert Introspector.db_type_to_ecto_type(repo, "cidr", "network") == :string
      assert Introspector.db_type_to_ecto_type(repo, "macaddr", "mac_address") == :string
    end

    @tag relations: [:postgres_geometric_types], adapter: :postgres
    test "correctly maps PostgreSQL geometric types", %{repo: repo} do
      # Test geometric types (mapped to string for now)
      assert Introspector.db_type_to_ecto_type(repo, "point", "location") == :string
      assert Introspector.db_type_to_ecto_type(repo, "line", "boundary") == :string
      assert Introspector.db_type_to_ecto_type(repo, "lseg", "segment") == :string
      assert Introspector.db_type_to_ecto_type(repo, "box", "rectangle") == :string
      assert Introspector.db_type_to_ecto_type(repo, "path", "route") == :string
      assert Introspector.db_type_to_ecto_type(repo, "polygon", "area") == :string
      assert Introspector.db_type_to_ecto_type(repo, "circle", "radius") == :string
    end

    @tag relations: [:postgres_array_types], adapter: :postgres
    test "correctly maps PostgreSQL array types", %{repo: repo} do
      # Test array types
      assert Introspector.db_type_to_ecto_type(repo, "integer[]", "numbers") ==
               {:array, :integer}

      assert Introspector.db_type_to_ecto_type(repo, "text[]", "tags") ==
               {:array, :string}

      assert Introspector.db_type_to_ecto_type(repo, "boolean[]", "flags") ==
               {:array, :boolean}

      assert Introspector.db_type_to_ecto_type(repo, "uuid[]", "identifiers") ==
               {:array, :binary_id}
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL range types", %{repo: repo} do
      # Test range types - should use EctoRange types if available, otherwise string
      if Code.ensure_loaded?(EctoRange) do
        assert Introspector.db_type_to_ecto_type(repo, "int4range", "number_range") ==
                 EctoRange.IntegerRange

        assert Introspector.db_type_to_ecto_type(repo, "int8range", "big_range") ==
                 EctoRange.IntegerRange

        assert Introspector.db_type_to_ecto_type(repo, "numrange", "decimal_range") ==
                 EctoRange.DecimalRange

        assert Introspector.db_type_to_ecto_type(repo, "tsrange", "time_range") ==
                 EctoRange.NaiveDateTimeRange

        assert Introspector.db_type_to_ecto_type(repo, "tstzrange", "tz_time_range") ==
                 EctoRange.DateTimeRange

        assert Introspector.db_type_to_ecto_type(repo, "daterange", "date_range") ==
                 EctoRange.DateRange
      else
        # Fallback to string when EctoRange is not available
        assert Introspector.db_type_to_ecto_type(repo, "int4range", "number_range") ==
                 :string

        assert Introspector.db_type_to_ecto_type(repo, "int8range", "big_range") ==
                 :string

        assert Introspector.db_type_to_ecto_type(repo, "numrange", "decimal_range") ==
                 :string

        assert Introspector.db_type_to_ecto_type(repo, "tsrange", "time_range") == :string

        assert Introspector.db_type_to_ecto_type(repo, "tstzrange", "tz_time_range") ==
                 :string

        assert Introspector.db_type_to_ecto_type(repo, "daterange", "date_range") ==
                 :string
      end
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "correctly maps PostgreSQL interval type", %{repo: repo} do
      # Test interval type (mapped to string for now)
      assert Introspector.db_type_to_ecto_type(repo, "interval", "duration") == :string
    end
  end

  # Helper function to find a column by name
  defp find_column(columns, name) do
    Enum.find(columns, &(&1.name == name)) ||
      raise "Column #{name} not found in #{inspect(Enum.map(columns, & &1.name))}"
  end
end
