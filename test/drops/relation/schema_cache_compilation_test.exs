defmodule Drops.Relation.SchemaCacheCompilationTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.SchemaCache

  describe "schema caching" do
    test "returns nil when cache miss occurs" do
      # Clear any existing cache
      SchemaCache.clear_all()

      # Simulate cache miss
      result = SchemaCache.get_cached_schema(Test.Repo, "test_table")

      # Verify we get nil for cache miss
      assert result == nil
    end

    test "returns cached schema when cache hit occurs" do
      # Clear cache first
      SchemaCache.clear_all()

      # Create a test schema to cache
      test_schema = create_test_schema("cached_table")

      # Cache it manually
      SchemaCache.cache_schema(Test.Repo, "cached_table", test_schema)

      # Now get_cached_schema should return the cached version
      cached_schema = SchemaCache.get_cached_schema(Test.Repo, "cached_table")

      # Verify we get the cached schema
      assert cached_schema.source == "cached_table"
    end

    test "cache_schema stores schema for later retrieval" do
      # Clear cache first
      SchemaCache.clear_all()

      test_schema = create_test_schema("inference_table")

      # Cache the schema
      SchemaCache.cache_schema(Test.Repo, "inference_table", test_schema)

      # Verify it was cached by retrieving it
      cached_schema = SchemaCache.get_cached_schema(Test.Repo, "inference_table")

      # Compare the cached schema
      assert cached_schema == test_schema
    end
  end

  defp create_test_schema(table_name) do
    alias Drops.Relation.Schema
    alias Drops.Relation.Schema.{Field, PrimaryKey, Indices}

    id_field = Field.new(:id, :integer, :id, :id)
    name_field = Field.new(:name, :string, :string, :name)

    # Only return the Drops.Relation.Schema - no more Ecto AST caching
    Schema.new(
      table_name,
      PrimaryKey.new([id_field]),
      # foreign_keys
      [],
      # fields
      [id_field, name_field],
      Indices.new(),
      # virtual_fields
      []
    )
  end
end
