defmodule Drops.Relations.AdvancedCompositionTest do
  use Drops.RelationCase, async: false

  # Define relations with associations
  relation(:users) do
    associations do
      has_many(:posts, Test.Relations.PostsSqlite.Struct, foreign_key: :user_id)
    end
  end

  relation(:posts) do
    associations do
      belongs_to(:user, Test.Relations.UsersSqlite.Struct, define_field: false)
    end
  end

  describe "preloading associations" do
    test "different relation types can be composed", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Admin User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Published Post",
          body: "Content",
          published: 1,
          user_id: user1.id
        })

      # Test composition between different relation types
      admin_users = users.restrict(name: "Admin User")
      result = posts.restrict(admin_users, published: 1)

      # Verify we get some kind of relation back
      assert result != nil
      assert is_struct(result)

      # Test that it works with basic operations
      case result do
        %Drops.Relation.Composite{} ->
          # If associations are detected, we get a Composite
          assert result.left == admin_users
          assert result.repo != nil

        _ ->
          # Otherwise, we get a regular relation
          assert result.__struct__ == posts
      end
    end

    test "composition with index-based finders", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Test User", email: "<EMAIL>"})

      {:ok, _post} =
        posts.insert(%{
          title: "Test Post",
          body: "Content",
          published: 1,
          user_id: user.id
        })

      # Test composition with get_by_* functions
      user_by_email = users.get_by_email("<EMAIL>")
      result = posts.restrict(user_by_email, published: 1)

      # Verify we get some kind of relation back
      assert result != nil
      assert is_struct(result)
    end

    test "composition with non-relation modules falls back gracefully", %{
      users: _users,
      posts: posts
    } do
      # Test with a regular queryable (should not create Composite)
      regular_query = from(u in "users", where: u.name == "Test")
      restricted = posts.restrict(regular_query, published: 1)

      # Should create a regular relation, not a Composite
      refute match?(%Drops.Relation.Composite{}, restricted)
      assert restricted.__struct__ == posts
    end

    test "users.preload(:posts) preloads posts association", %{
      users: users,
      posts: posts
    } do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Admin User", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "Regular User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Admin Post 1",
          body: "Content 1",
          published: 1,
          user_id: user1.id
        })

      {:ok, _post2} =
        posts.insert(%{
          title: "Admin Post 2",
          body: "Content 2",
          published: 1,
          user_id: user1.id
        })

      {:ok, _post3} =
        posts.insert(%{
          title: "User Post",
          body: "Content 3",
          published: 1,
          user_id: user2.id
        })

      # Use the new preload API
      users_with_posts = users.preload(:posts)

      # Enumerate the results - this should trigger preloading
      results = Enum.to_list(users_with_posts)

      # Should get both users with posts preloaded
      assert length(results) == 2

      # Verify all users have posts preloaded (not lazy-loaded)
      Enum.each(results, fn user ->
        assert Ecto.assoc_loaded?(user.posts)
      end)

      # Find the admin user and verify their posts
      admin_user = Enum.find(results, &(&1.name == "Admin User"))
      assert admin_user != nil
      assert admin_user.email == "<EMAIL>"
      assert length(admin_user.posts) == 2

      admin_post_titles = Enum.map(admin_user.posts, & &1.title) |> Enum.sort()
      assert admin_post_titles == ["Admin Post 1", "Admin Post 2"]

      # Find the regular user and verify their posts
      regular_user = Enum.find(results, &(&1.name == "Regular User"))
      assert regular_user != nil
      assert regular_user.email == "<EMAIL>"
      assert length(regular_user.posts) == 1
      assert hd(regular_user.posts).title == "User Post"
    end

    test "preload works with restricted relations", %{
      users: users,
      posts: posts
    } do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Test User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Published Post",
          body: "Content 1",
          published: 1,
          user_id: user.id
        })

      {:ok, _post2} =
        posts.insert(%{
          title: "Draft Post",
          body: "Content 2",
          published: 0,
          user_id: user.id
        })

      # Use preload with restricted relations
      test_users_with_posts = users.restrict(name: "Test User") |> users.preload(:posts)

      # Enumerate the results
      results = Enum.to_list(test_users_with_posts)

      # Should get the user with all posts preloaded (both published and draft)
      assert length(results) == 1
      [found_user] = results

      assert found_user.email == "<EMAIL>"
      assert Ecto.assoc_loaded?(found_user.posts)
      assert length(found_user.posts) == 2

      # Verify both posts are loaded
      post_titles = Enum.map(found_user.posts, & &1.title) |> Enum.sort()
      assert post_titles == ["Draft Post", "Published Post"]
    end

    test "preload works with index-based finders", %{
      users: users,
      posts: posts
    } do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Test User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Published Post",
          body: "Content 1",
          published: 1,
          user_id: user.id
        })

      # Use index-based finder with preload
      user_with_posts = users.get_by_email("<EMAIL>") |> users.preload(:posts)

      # Enumerate the results
      results = Enum.to_list(user_with_posts)

      # Should get the user with posts preloaded
      assert length(results) == 1
      [found_user] = results

      assert found_user.email == "<EMAIL>"
      assert Ecto.assoc_loaded?(found_user.posts)
      assert length(found_user.posts) == 1
      assert hd(found_user.posts).title == "Published Post"
    end

    test "preload works with multiple associations", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Multi User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Post 1",
          body: "Content 1",
          published: 1,
          user_id: user.id
        })

      {:ok, _post2} =
        posts.insert(%{
          title: "Post 2",
          body: "Content 2",
          published: 0,
          user_id: user.id
        })

      # Test preloading multiple associations (even though we only have posts in this test)
      users_with_posts = users.preload([:posts])

      results = Enum.to_list(users_with_posts)
      multi_user = Enum.find(results, &(&1.name == "Multi User"))

      assert multi_user != nil
      assert Ecto.assoc_loaded?(multi_user.posts)
      assert length(multi_user.posts) == 2
    end

    test "preload preserves existing restrictions", %{users: users, posts: posts} do
      # Insert test data for multiple users
      {:ok, user1} = users.insert(%{name: "Alice", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "Bob", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Alice Post",
          body: "Content",
          published: 1,
          user_id: user1.id
        })

      {:ok, _post2} =
        posts.insert(%{
          title: "Bob Post",
          body: "Content",
          published: 1,
          user_id: user2.id
        })

      # Restrict to only Alice, then preload posts
      alice_with_posts = users.restrict(name: "Alice") |> users.preload(:posts)

      results = Enum.to_list(alice_with_posts)

      # Should only get Alice, not Bob
      assert length(results) == 1
      [alice] = results

      assert alice.name == "Alice"
      assert Ecto.assoc_loaded?(alice.posts)
      assert length(alice.posts) == 1
      assert hd(alice.posts).title == "Alice Post"
    end

    test "chaining multiple preloads works", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Chain User", email: "<EMAIL>"})

      {:ok, _post} =
        posts.insert(%{
          title: "Chain Post",
          body: "Content",
          published: 1,
          user_id: user.id
        })

      # Chain multiple preload calls (though we only have one association type in this test)
      users_with_posts = users.preload(:posts) |> users.preload(:posts)

      results = Enum.to_list(users_with_posts)
      chain_user = Enum.find(results, &(&1.name == "Chain User"))

      assert chain_user != nil
      assert Ecto.assoc_loaded?(chain_user.posts)
      assert length(chain_user.posts) == 1
    end
  end
end
