defmodule Mix.Tasks.Test.Group do
  @moduledoc """
  Runs tests for a specific component group.

  ## Usage

      mix test.group relation
      mix test.group relations
      mix test.group schema
      mix test.group contract
      mix test.group operations
      mix test.group type
      mix test.group integration

  This is equivalent to running:

      mix test test/drops/{group}*

  ## Examples

      # Run all relation tests
      mix test.group relation

      # Run all operations tests
      mix test.group operations

      # Run integration tests
      mix test.group integration
  """

  use Mix.Task

  @shortdoc "Runs tests for a specific component group"

  @groups %{
    "relation" => "test/drops/relation*",
    "schema" => "test/drops/schema*",
    "contract" => "test/drops/contract*",
    "operations" => "test/drops/operations*",
    "type" => "test/drops/type*",
    "integration" => "test/integration*"
  }

  def run([group | args])
      when group in ~w(relation relations schema contract operations type integration) do
    pattern = Map.get(@groups, group)

    if pattern do
      # Expand the glob pattern to actual file paths
      files = Path.wildcard(pattern)

      if files == [] do
        Mix.shell().error("No test files found matching pattern: #{pattern}")
      else
        Mix.Task.run("test", files ++ args)
      end
    else
      Mix.shell().error("Unknown test group: #{group}")
      show_usage()
    end
  end

  def run([group | _args]) do
    Mix.shell().error("Unknown test group: #{group}")
    show_usage()
  end

  def run([]) do
    Mix.shell().error("Please specify a test group")
    show_usage()
  end

  defp show_usage do
    Mix.shell().info("""
    Available test groups:

      relation      - test/drops/relation*
      relations     - test/drops/relations*
      schema        - test/drops/schema*
      contract      - test/drops/contract*
      operations    - test/drops/operations*
      type          - test/drops/type*
      integration   - test/integration*

    Usage: mix test.group <group> [test options]
    """)
  end

  # Convenience functions for aliases
  def run_relation(args), do: run(["relation" | args])
  def run_relations(args), do: run(["relations" | args])
  def run_schema(args), do: run(["schema" | args])
  def run_contract(args), do: run(["contract" | args])
  def run_operations(args), do: run(["operations" | args])
  def run_type(args), do: run(["type" | args])
  def run_integration(args), do: run(["integration" | args])
end
