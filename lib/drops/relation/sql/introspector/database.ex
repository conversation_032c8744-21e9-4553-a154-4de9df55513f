defmodule Drops.Relation.SQL.Introspector.Database do
  @moduledoc """
  Behavior for database-specific introspection operations.

  This behavior defines the interface that database adapters must implement
  to support schema introspection in Drops.Relation. Each database adapter
  provides its own implementation of these callbacks to handle database-specific
  queries and type conversions.

  ## Callbacks

  - `get_table_indices/2` - Extract index information from the database
  - `introspect_table_columns/2` - Get column metadata for a table
  - `db_type_to_ecto_type/2` - Convert database types to Ecto types
  - `index_type_to_atom/1` - Convert database index types to atoms

  ## Implementations

  - `Drops.Relation.SQL.Introspector.Database.SQLite` - SQLite adapter
  - `Drops.Relation.SQL.Introspector.Database.Postgres` - PostgreSQL adapter

  ## Example

      defmodule MyCustomAdapter do
        @behaviour Drops.Relation.SQL.Introspector.Database

        @impl true
        def get_table_indices(repo, table_name) do
          # Custom implementation for your database
        end

        @impl true
        def introspect_table_columns(repo, table_name) do
          # Custom implementation for your database
        end

        @impl true
        def db_type_to_ecto_type(db_type, field_name) do
          # Custom type mapping for your database
        end

        @impl true
        def index_type_to_atom(index_type) do
          # Custom index type mapping for your database
        end
      end
  """

  alias Drops.Relation.Schema.Indices

  @doc """
  Extracts index information for a table from the database.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_name` - The name of the table to introspect

  ## Returns

  Returns `{:ok, %Indices{}}` on success or `{:error, reason}` on failure.

  ## Examples

      iex> MyAdapter.get_table_indices(MyRepo, "users")
      {:ok, %Drops.Relation.Schema.Indices{indices: [...]}}
  """
  @callback get_table_indices(module(), String.t()) :: {:ok, Indices.t()} | {:error, term()}

  @doc """
  Introspects database table columns using database-specific queries.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_name` - The name of the table to introspect

  ## Returns

  A list of column metadata maps with keys:
  - `:name` - Column name as string
  - `:type` - Database type as string
  - `:not_null` - Boolean indicating if column is NOT NULL
  - `:primary_key` - Boolean indicating if column is part of primary key

  ## Examples

      iex> MyAdapter.introspect_table_columns(MyRepo, "users")
      [%{name: "id", type: "INTEGER", not_null: true, primary_key: true}]
  """
  @callback introspect_table_columns(module(), String.t()) :: [map()]

  @doc """
  Converts database types to Ecto types.

  ## Parameters

  - `db_type` - The database type as a string
  - `field_name` - The field name (used for foreign key detection)

  ## Returns

  An Ecto type atom.

  ## Examples

      iex> MyAdapter.db_type_to_ecto_type("INTEGER", "user_id")
      :id
      iex> MyAdapter.db_type_to_ecto_type("TEXT", "name")
      :string
  """
  @callback db_type_to_ecto_type(String.t(), String.t()) :: atom()

  @doc """
  Converts database-specific index types to atoms.

  ## Parameters

  - `index_type` - The database-specific index type as a string

  ## Returns

  An atom representing the index type, or `nil` if unknown.

  ## Examples

      iex> MyAdapter.index_type_to_atom("btree")
      :btree
      iex> MyAdapter.index_type_to_atom("unknown_type")
      nil
  """
  @callback index_type_to_atom(String.t()) :: atom() | nil
end
