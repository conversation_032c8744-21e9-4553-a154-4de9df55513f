defmodule Drops.Relation.SQL.Introspector.Database.SQLite do
  @moduledoc """
  SQLite implementation of the Database behavior for schema introspection.

  This module provides SQLite-specific implementations for database introspection
  operations using SQLite's PRAGMA statements and system tables.

  ## Features

  - Index introspection via PRAGMA statements
  - Column metadata extraction via PRAGMA table_info
  - SQLite type to Ecto type conversion
  - Support for unique and composite indices
  """

  @behaviour Drops.Relation.SQL.Introspector.Database

  alias Drops.Relation.Schema.{Index, Indices}

  @impl true
  def get_table_indices(repo, table_name) do
    # SQLite PRAGMA to get index list
    index_list_query = "PRAGMA index_list(#{table_name})"

    case repo.query(index_list_query) do
      {:ok, %{rows: rows}} ->
        # PRAGMA index_list returns: [seq, name, unique, origin, partial]
        indices =
          for [_seq, name, unique, _origin, _partial] <- rows do
            # Get index details
            index_info_query = "PRAGMA index_info(#{name})"

            case repo.query(index_info_query) do
              {:ok, %{rows: info_rows}} ->
                # PRAGMA index_info returns: [seqno, cid, name]
                field_names =
                  info_rows
                  # Sort by seqno
                  |> Enum.sort_by(&hd/1)
                  |> Enum.map(fn [_seqno, _cid, field_name] ->
                    String.to_atom(field_name)
                  end)

                Index.from_names(name, field_names, unique == 1, :btree)

              {:error, _} ->
                # If we can't get field info, create index with empty fields
                Index.from_names(name, [], unique == 1, :btree)
            end
          end
          |> Enum.reject(&is_nil/1)

        {:ok, Indices.new(indices)}

      {:error, error} ->
        {:error, error}
    end
  end

  @impl true
  def introspect_table_columns(repo, table_name) do
    # Use SQLite PRAGMA table_info to get column information
    query = "PRAGMA table_info(#{table_name})"

    case repo.query(query) do
      {:ok, %{rows: rows, columns: _columns}} ->
        # PRAGMA table_info returns: [cid, name, type, notnull, dflt_value, pk]
        columns =
          Enum.map(rows, fn [_cid, name, type, notnull, dflt_value, pk] ->
            %{
              name: name,
              type: type,
              not_null: notnull == 1,
              primary_key: pk == 1,
              default: parse_default_value(dflt_value),
              is_nullable: notnull != 1
            }
          end)

        # Enhance with check constraints
        enhance_with_check_constraints(repo, table_name, columns)

      {:error, error} ->
        raise "Failed to introspect table #{table_name}: #{inspect(error)}"
    end
  end

  @impl true
  def db_type_to_ecto_type(sqlite_type, field_name) do
    # Normalize the type string for comparison
    normalized_type = String.upcase(sqlite_type)

    case normalized_type do
      # Core SQLite native types
      "INTEGER" ->
        :integer

      "TEXT" ->
        # In SQLite, binary_id fields are stored as TEXT
        # We need to detect them based on field name patterns
        if binary_id_field?(field_name) do
          :binary_id
        else
          :string
        end

      "REAL" ->
        :float

      "BLOB" ->
        :binary

      "NUMERIC" ->
        :decimal

      "DECIMAL" ->
        :decimal

      # SQLite interpreted types (stored as other types but interpreted by applications)
      "BOOLEAN" ->
        :boolean

      "BOOL" ->
        :boolean

      "DATE" ->
        :date

      "DATETIME" ->
        :naive_datetime

      "TIMESTAMP" ->
        :naive_datetime

      "TIME" ->
        :time

      "JSON" ->
        :map

      # Additional interpreted types
      "FLOAT" ->
        :float

      "DOUBLE" ->
        :float

      "CLOB" ->
        :string

      # Common SQLite type variations
      "INT" ->
        :integer

      "STRING" ->
        :string

      # Handle parameterized and unknown types
      _ ->
        cond do
          String.starts_with?(normalized_type, "DECIMAL(") -> :decimal
          String.starts_with?(normalized_type, "NUMERIC(") -> :decimal
          String.starts_with?(normalized_type, "VARCHAR(") -> :string
          String.starts_with?(normalized_type, "CHAR(") -> :string
          true -> :string
        end
    end
  end

  # Helper function to detect boolean fields in SQLite

  @impl true
  def index_type_to_atom(type_string) do
    case String.downcase(type_string) do
      "" -> nil
      "btree" -> :btree
      # SQLite primarily uses btree indices, but return nil for unknown types
      _ -> nil
    end
  end

  # Private helper functions

  defp parse_default_value(nil), do: nil
  defp parse_default_value(""), do: nil

  defp parse_default_value(value) when is_binary(value) do
    # Remove quotes if present - handle both single and double quotes properly
    trimmed =
      value
      |> String.trim()
      |> String.trim("'")
      |> String.trim("\"")

    # Try to parse as different types
    cond do
      trimmed == "NULL" ->
        nil

      trimmed == "CURRENT_TIMESTAMP" ->
        :current_timestamp

      trimmed == "CURRENT_DATE" ->
        :current_date

      trimmed == "CURRENT_TIME" ->
        :current_time

      String.match?(trimmed, ~r/^\d+$/) ->
        String.to_integer(trimmed)

      String.match?(trimmed, ~r/^\d+\.\d+$/) ->
        String.to_float(trimmed)

      String.downcase(trimmed) in ["true", "false"] ->
        String.to_existing_atom(String.downcase(trimmed))

      true ->
        trimmed
    end
  end

  defp parse_default_value(value), do: value

  defp enhance_with_check_constraints(repo, table_name, columns) do
    # SQLite stores check constraints in the CREATE TABLE statement
    # We can extract them from sqlite_master
    query = """
    SELECT sql FROM sqlite_master
    WHERE type = 'table' AND name = ?
    """

    case repo.query(query, [table_name]) do
      {:ok, %{rows: [[sql]]}} when is_binary(sql) ->
        check_constraints = extract_check_constraints_from_sql(sql)

        Enum.map(columns, fn column ->
          column_constraints =
            Enum.filter(check_constraints, fn constraint ->
              String.contains?(constraint, column.name)
            end)

          Map.put(column, :check_constraints, column_constraints)
        end)

      _ ->
        # If we can't get the SQL, just add empty check constraints
        Enum.map(columns, &Map.put(&1, :check_constraints, []))
    end
  end

  defp extract_check_constraints_from_sql(sql) do
    # Simple regex to extract CHECK constraints
    # This is a basic implementation - could be enhanced for more complex cases
    ~r/CHECK\s*\(([^)]+)\)/i
    |> Regex.scan(sql, capture: :all_but_first)
    |> List.flatten()
    |> Enum.map(&String.trim/1)
  end

  # Helper function to detect binary_id fields in SQLite
  # Since SQLite stores binary_id as TEXT, we need heuristics to detect them
  defp binary_id_field?(field_name) do
    case field_name do
      # Primary key named 'id' is likely binary_id if it's TEXT
      "id" -> true
      # Foreign key fields ending with '_id' might be binary_id
      # This is a heuristic - in a real implementation, you might want to
      # check if the referenced table has a binary_id primary key
      field when is_binary(field) -> String.ends_with?(field, "_id")
      _ -> false
    end
  end
end
