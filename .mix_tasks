app.config:Configures all registered apps
app.start:Starts all registered apps
app.tree:Prints the application tree
archive:Lists installed archives
archive.build:Archives this project into a .ez file
archive.install:Installs an archive locally
archive.uninstall:Uninstalls archives
clean:Deletes generated application files
cmd:Executes the given command
compile:Compiles source files
credo:Run code analysis (use `--help` for options)
credo.gen.check:Generate a new custom check for Credo
credo.gen.config:Generate a new config for Credo
deps:Lists dependencies and their status
deps.clean:Deletes the given dependencies' files
deps.compile:Compiles dependencies
deps.get:Gets all out of date dependencies
deps.tree:Prints the dependency tree
deps.unlock:Unlocks the given dependencies
deps.update:Updates the given dependencies
dialyzer:Runs dialyzer with default or project-defined flags.
dialyzer.build:Build the required PLT(s) and exit.
dialyzer.clean:Delete PLT(s) and exit.
dialyzer.explain:Display information about Dialyzer warnings.
do:Executes the tasks separated by plus
docs:Generate documentation for the project
doctor:Documentation coverage report
doctor.explain:Debug why a particular module is failing validation
doctor.gen.config:Creates a .doctor.exs config file with defaults
drops.dev.setup:Sets up the development environment for Drops
drops.example:Runs a Drops example with proper environment setup and timing measurement
drops.examples:Lists all available Drops examples
ecto:Prints Ecto help information
ecto.create:Alias for drops.dev.setup, ecto.create
ecto.create:Creates the repository storage
ecto.drop:Alias for drops.dev.setup, ecto.drop --force-drop
ecto.drop:Drops the repository storage
ecto.dump:Dumps the repository database structure
ecto.gen.migration:Generates a new migration for the repo
ecto.gen.repo:Generates a new repository
ecto.load:Loads previously dumped database structure
ecto.migrate:Alias for drops.dev.setup, ecto.migrate
ecto.migrate:Runs the repository migrations
ecto.migrations:Displays the repository migration status
ecto.reset:Alias for drops.dev.setup, ecto.drop --force-drop, ecto.setup
ecto.rollback:Rolls back the repository migrations
ecto.setup:Alias for drops.dev.setup, ecto.create, ecto.migrate
elixir_make.checksum:Fetch precompiled NIFs and build the checksums
elixir_make.precompile:Precompiles the given project for all targets
escript:Lists installed escripts
escript.build:Builds an escript for the project
escript.install:Installs an escript locally
escript.uninstall:Uninstalls escripts
eval:Evaluates the given code
format:Formats the given files/patterns
help:Prints help information for tasks
hex:Prints Hex help information
hex.audit:Shows retired Hex deps for the current project
hex.build:Builds a new package version locally
hex.config:Reads, updates or deletes local Hex config
hex.docs:Fetches or opens documentation of a package
hex.info:Prints Hex information
hex.organization:Manages Hex.pm organizations
hex.outdated:Shows outdated Hex deps for the current project
hex.owner:Manages Hex package ownership
hex.package:Fetches or diffs packages
hex.publish:Publishes a new package version
hex.registry:Manages local Hex registries
hex.repo:Manages Hex repositories
hex.retire:Retires a package version
hex.search:Searches for package names
hex.sponsor:Show Hex packages accepting sponsorships
hex.user:Manages your Hex user account
igniter.add:Adds the provided deps to `mix.exs`
igniter.add_extension:Adds an extension to your `.igniter.exs` configuration file.
igniter.apply_upgrades:Applies the upgrade scripts for the list of package version changes provided.
igniter.gen.task:Generates a new igniter task
igniter.install:Install a package or packages, and run any associated installers.
igniter.move_files:Moves any relevant files to their 'correct' location.
igniter.refactor.rename_function:Rename functions across a project with automatic reference updates.
igniter.refactor.unless_to_if_not:Rewrites occurrences of `unless x` to `if !x` across the project.
igniter.remove:Removes the provided deps from `mix.exs`
igniter.setup:Creates or updates a .igniter.exs file, used to configure Igniter for end user's preferences.
igniter.update_gettext:Applies changes to resolve a warning introduced in gettext 0.26.0
igniter.upgrade:Fetch and upgrade dependencies. A drop in replacement for `mix deps.update` that also runs upgrade tasks.
loadconfig:Loads and persists the given configuration
local:Lists tasks installed locally via archives
local.hex:Installs Hex locally
local.public_keys:Manages public keys
local.rebar:Installs Rebar locally
new:Creates a new Elixir project
nimble_parsec.compile:Compiles a parser and injects its content into the parser file
profile.cprof:Profiles the given file or expression with cprof
profile.eprof:Profiles the given file or expression with eprof
profile.fprof:Profiles the given file or expression with fprof
profile.tprof:Profiles the given file or expression with tprof
release:Assembles a self-contained release
release.init:Generates sample files for releases
run:Runs the current application
test:Runs a project's tests
test.coverage:Build report from exported test coverage
test.group:Runs tests for a specific component group
xref:Prints cross reference information
